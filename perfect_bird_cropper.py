#!/usr/bin/env python3
"""
Perfect Bird Cropper - Optimized Edge Detection
===============================================

This script provides the most reliable and precise bird cropping using
optimized edge detection algorithms specifically tuned for bird images.

Author: AI Assistant
Version: 1.0.0
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
import argparse
import logging
from pathlib import Path
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerfectBirdCropper:
    """Perfect bird cropping with optimized edge detection"""
    
    def __init__(self, padding_pixels: int = 2):
        self.padding_pixels = padding_pixels
    
    def crop_perfect_bird(self, image_path: str, output_path: str = None) -> str:
        """
        Crop bird image with perfect edge detection
        
        Args:
            image_path: Path to input image
            output_path: Path for output image (optional)
            
        Returns:
            Path to cropped image
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Failed to load image: {image_path}")
            
            logger.info(f"Processing: {image_path}")
            logger.info(f"Original size: {image.shape[1]}x{image.shape[0]}")
            
            # Find bird using optimized detection
            crop_region = self._find_bird_with_optimized_detection(image)
            
            if crop_region is None:
                logger.warning("Using fallback detection method")
                crop_region = self._fallback_detection(image)
            
            # Apply crop
            cropped_image = self._apply_crop(image, crop_region)
            
            # Enhance result
            enhanced_image = self._enhance_result(cropped_image)
            
            # Generate output path if not provided
            if output_path is None:
                input_path = Path(image_path)
                timestamp = int(time.time())
                output_path = input_path.parent / f"{input_path.stem}_perfect_bird_{timestamp}.jpg"
            
            # Save with high quality
            cv2.imwrite(str(output_path), enhanced_image, [cv2.IMWRITE_JPEG_QUALITY, 98])
            
            logger.info(f"Perfect bird crop saved: {output_path}")
            logger.info(f"Final size: {enhanced_image.shape[1]}x{enhanced_image.shape[0]}")
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error in perfect bird cropping: {e}")
            raise
    
    def _find_bird_with_optimized_detection(self, image: np.ndarray) -> tuple:
        """Find bird using optimized detection methods"""
        try:
            # Method 1: Advanced edge detection
            edge_region = self._optimized_edge_detection(image)
            if edge_region and self._is_valid_bird_region(edge_region, image.shape):
                logger.info("Using optimized edge detection")
                return edge_region
            
            # Method 2: Color-based detection
            color_region = self._optimized_color_detection(image)
            if color_region and self._is_valid_bird_region(color_region, image.shape):
                logger.info("Using optimized color detection")
                return color_region
            
            # Method 3: Contour-based detection
            contour_region = self._optimized_contour_detection(image)
            if contour_region and self._is_valid_bird_region(contour_region, image.shape):
                logger.info("Using optimized contour detection")
                return contour_region
            
            return None
            
        except Exception as e:
            logger.error(f"Error in optimized detection: {e}")
            return None
    
    def _optimized_edge_detection(self, image: np.ndarray) -> tuple:
        """Optimized edge detection for bird images"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply bilateral filter to reduce noise while preserving edges
            filtered = cv2.bilateralFilter(gray, 9, 75, 75)
            
            # Multi-threshold Canny edge detection
            edges1 = cv2.Canny(filtered, 30, 80)
            edges2 = cv2.Canny(filtered, 50, 120)
            edges3 = cv2.Canny(filtered, 70, 150)
            
            # Combine edges
            combined_edges = cv2.bitwise_or(edges1, edges2)
            combined_edges = cv2.bitwise_or(combined_edges, edges3)
            
            # Morphological operations to connect edges
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            combined_edges = cv2.morphologyEx(combined_edges, cv2.MORPH_CLOSE, kernel)
            combined_edges = cv2.dilate(combined_edges, kernel, iterations=1)
            
            # Find contours and get the largest meaningful one
            contours, _ = cv2.findContours(combined_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Filter contours by area (bird should be significant portion of image)
            min_area = image.shape[0] * image.shape[1] * 0.01  # At least 1% of image
            valid_contours = [c for c in contours if cv2.contourArea(c) > min_area]
            
            if not valid_contours:
                return None
            
            # Get the largest contour
            largest_contour = max(valid_contours, key=cv2.contourArea)
            
            # Get bounding rectangle with padding
            x, y, w, h = cv2.boundingRect(largest_contour)
            return self._add_padding(x, y, w, h, image.shape)
            
        except Exception as e:
            logger.error(f"Error in optimized edge detection: {e}")
            return None
    
    def _optimized_color_detection(self, image: np.ndarray) -> tuple:
        """Optimized color detection for bird images"""
        try:
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Define bird color ranges (optimized for common bird colors)
            bird_ranges = [
                # Brown birds (most common)
                ([5, 40, 40], [25, 255, 255]),
                # Gray birds
                ([0, 0, 30], [180, 40, 180]),
                # Dark birds
                ([0, 0, 0], [180, 255, 70]),
                # Colorful birds (red, blue, etc.)
                ([0, 60, 60], [180, 255, 255])
            ]
            
            # Create combined mask
            combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
            
            for lower, upper in bird_ranges:
                lower = np.array(lower)
                upper = np.array(upper)
                mask = cv2.inRange(hsv, lower, upper)
                combined_mask = cv2.bitwise_or(combined_mask, mask)
            
            # Clean up mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Get largest contour
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Check if contour is large enough
            if cv2.contourArea(largest_contour) < image.shape[0] * image.shape[1] * 0.005:
                return None
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)
            return self._add_padding(x, y, w, h, image.shape)
            
        except Exception as e:
            logger.error(f"Error in optimized color detection: {e}")
            return None
    
    def _optimized_contour_detection(self, image: np.ndarray) -> tuple:
        """Optimized contour detection for bird images"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply adaptive thresholding
            adaptive_thresh = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 15, 3
            )
            
            # Morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            adaptive_thresh = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(adaptive_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Filter and find best contour
            min_area = image.shape[0] * image.shape[1] * 0.01
            valid_contours = [c for c in contours if cv2.contourArea(c) > min_area]
            
            if not valid_contours:
                return None
            
            # Get largest contour
            largest_contour = max(valid_contours, key=cv2.contourArea)
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)
            return self._add_padding(x, y, w, h, image.shape)
            
        except Exception as e:
            logger.error(f"Error in optimized contour detection: {e}")
            return None
    
    def _fallback_detection(self, image: np.ndarray) -> tuple:
        """Fallback detection method"""
        try:
            # Simple center crop with intelligent bias
            height, width = image.shape[:2]
            
            # Assume bird is in center 80% of image, biased towards upper portion
            crop_width = int(width * 0.8)
            crop_height = int(height * 0.8)
            
            # Center horizontally, bias towards upper 30%
            x = (width - crop_width) // 2
            y = int((height - crop_height) * 0.3)
            
            logger.info("Using fallback center crop with upper bias")
            return (x, y, crop_width, crop_height)
            
        except Exception as e:
            logger.error(f"Error in fallback detection: {e}")
            # Ultimate fallback - return original image bounds
            return (0, 0, image.shape[1], image.shape[0])
    
    def _is_valid_bird_region(self, region: tuple, image_shape: tuple) -> bool:
        """Check if detected region is valid for a bird"""
        x, y, w, h = region
        height, width = image_shape[:2]
        
        # Check if region is reasonable size (not too small or too large)
        area_ratio = (w * h) / (width * height)
        
        # Bird should be between 5% and 95% of image
        if area_ratio < 0.05 or area_ratio > 0.95:
            return False
        
        # Check aspect ratio (birds are usually not extremely elongated)
        aspect_ratio = w / h if h > 0 else 0
        if aspect_ratio < 0.2 or aspect_ratio > 5.0:
            return False
        
        return True
    
    def _add_padding(self, x: int, y: int, w: int, h: int, image_shape: tuple) -> tuple:
        """Add padding to crop region"""
        height, width = image_shape[:2]
        
        # Add padding
        x = max(0, x - self.padding_pixels)
        y = max(0, y - self.padding_pixels)
        w = min(width - x, w + 2 * self.padding_pixels)
        h = min(height - y, h + 2 * self.padding_pixels)
        
        return (x, y, w, h)
    
    def _apply_crop(self, image: np.ndarray, crop_region: tuple) -> np.ndarray:
        """Apply crop to image"""
        x, y, w, h = crop_region
        cropped = image[y:y+h, x:x+w]
        logger.info(f"Crop region: x={x}, y={y}, w={w}, h={h}")
        return cropped
    
    def _enhance_result(self, image: np.ndarray) -> np.ndarray:
        """Enhance the cropped result"""
        try:
            # Convert to PIL for enhancement
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(1.3)
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.15)
            
            # Convert back to OpenCV format
            enhanced = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return enhanced
            
        except Exception as e:
            logger.error(f"Error enhancing image: {e}")
            return image


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Perfect Bird Cropper - Optimized Edge Detection",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Perfect bird crop with minimal padding
  python perfect_bird_cropper.py --input 00_original.png
  
  # Perfect bird crop with zero padding
  python perfect_bird_cropper.py --input 00_original.png --padding 0
  
  # Perfect bird crop with custom output
  python perfect_bird_cropper.py --input 00_original.png --output perfect_bird_final.jpg
        """
    )
    
    parser.add_argument('--input', '-i', type=str, required=True, help='Input image path')
    parser.add_argument('--output', '-o', type=str, help='Output image path (optional)')
    parser.add_argument('--padding', '-p', type=int, default=2, help='Padding pixels around bird (default: 2)')
    
    args = parser.parse_args()
    
    # Create perfect bird cropper instance
    cropper = PerfectBirdCropper(padding_pixels=args.padding)
    
    # Process the image
    try:
        result = cropper.crop_perfect_bird(args.input, args.output)
        print(f"✅ Successfully created perfect bird crop: {result}")
    except Exception as e:
        print(f"❌ Failed to crop image: {e}")


if __name__ == "__main__":
    main()
