from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import os
import time
from urllib import request as wget
import tqdm as tqdm
import argparse
import requests
import json
from urllib.parse import urljoin, urlparse
from PIL import Image, ImageDraw
import cv2
import numpy as np
'''

wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'utils\\chromedriver.exe'))
wd.get('https://google.com')
search = wd.find_element_by_css_selector('input.gLFyf')
search.send_keys('')

'''

class ScraperBot():
    def __init__(self):
        self.wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'chromedriver.exe'))
        self.urlbase = "https://google.com/search?tbm=isch&q={}"

    def _initiateSession(self, key):
        self.wd.get(self.urlbase.format(key))

    def _acceptCookies(self):
        try:
            self.wd.execute_script("document.getElementsByClassName('USRMqe')[0].style.display = 'none';")
        except:
            pass

    def _endOfPage(self):
        try:
            self.wd.find_element_by_class_name('OuJzKb Yu2Dnd')
            print("no more files")
        except:
            pass

        try:
            self.wd.find_element_by_class_name('mye4qd').click()
            time.sleep(1)
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        except:
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")


    def _deduplicate(self, listOfurls):
        try:
            inputList = list(set(listOfurls))
            return inputList
        except:
            pass

    def _checkOpenTabs(self):
        browserTabs = self.wd.window_handles
        if len(browserTabs) > 1:
            self.wd.switch_to.window(browserTabs[1])
            self.wd.close()
            self.wd.switch_to.window(browserTabs[0])

    def _getURL(self):
        thumbs = self.wd.find_elements_by_css_selector('img.Q4LuWd')
        urls = []
        for thumbImg in thumbs:
            try:
                thumbImg.click()
                actualImg = self.wd.find_elements_by_css_selector('img.n3VNCb')

                for imageData in actualImg:
                    if 'https' in imageData.get_attribute('src'):
                        urls.append(imageData.get_attribute('src'))

                self._checkOpenTabs()
            except:
                pass
        return urls

    def _totalImages(self, dir):
        count = 0
        for filename in os.listdir(dir):
            if filename.endswith('.jpg'):
                count += 1
            else:
                continue
        return count

    def _downloader(self, data, key, out_dir):
        key = key.replace(" ", "_")
        DIR1 = os.path.join(out_dir, key)

        try:
            os.mkdir(DIR1)
        except:
            pass

        for idx in tqdm.tqdm(range(len(data))):
            filename = "{}-{}.jpg".format(key, idx)
            PATH = os.path.join(DIR1, '{}'.format(filename))

            try:
                print("downloading next batch")
                wget.urlretrieve(str(data[idx]), PATH)
            except:
                pass


    def scrape(self, search, min_image_count, directory):
        self._initiateSession(key=search)
        self._acceptCookies()

        totalImageCount = 0
        while totalImageCount < min_image_count:
            urlList = self._deduplicate(self._getURL())
            self._downloader(data=urlList,
                             key=search,
                             out_dir=directory)
            urlList.clear()
            totalImageCount = self._totalImages(os.path.join(os.getcwd(), search.replace(" ", "_")))
            print("current Image count: {}".format(totalImageCount))
            self._endOfPage()
            time.sleep(2)

        if totalImageCount >= min_image_count:
            self.wd.quit()


class EBirdScraperBot():
    def __init__(self, headless=False, custom_resolution=None):
        # Initialize ultra quality mode
        self.ultra_quality_mode = False
        self.custom_resolution = custom_resolution or (1920, 1080)

        # Asset ID tracking system for unique image validation
        self._downloaded_asset_ids = set()
        self._current_session_stats = {
            'attempted': 0,
            'unique_downloads': 0,
            'duplicates_skipped': 0,
            'failed_detections': 0
        }
        self._original_gallery_url = None

        chrome_options = Options()
        if headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")

        # Enhanced window size for better screenshots
        width, height = self.custom_resolution
        chrome_options.add_argument(f"--window-size={width},{height}")
        chrome_options.add_argument("--start-maximized")

        # High quality rendering options
        chrome_options.add_argument("--force-device-scale-factor=1")
        chrome_options.add_argument("--high-dpi-support=1")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")

        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option('useAutomationExtension', False)

        # Gunakan webdriver-manager untuk otomatis download ChromeDriver
        try:
            service = Service(ChromeDriverManager().install())
            self.wd = webdriver.Chrome(service=service, options=chrome_options)
        except Exception as e:
            print(f"Error initializing Chrome driver: {e}")
            # Fallback ke cara lama jika webdriver-manager gagal
            try:
                self.wd = webdriver.Chrome(executable_path=os.path.join(os.getcwd(), 'chromedriver.exe'), options=chrome_options)
            except:
                self.wd = webdriver.Chrome(options=chrome_options)

        # Disable webdriver detection
        self.wd.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

        # Set maximum window size for better screenshots
        self.wd.maximize_window()

        self.wait = WebDriverWait(self.wd, 10)
        self.base_url = "https://media.ebird.org"

    def _scroll_and_load_more(self, timeout_seconds=300, max_load_more_clicks=None):
        """Enhanced load more functionality with configurable click limits"""
        if max_load_more_clicks is None:
            print("🔄 Starting UNLIMITED LOAD MORE process...")
            print("📋 Will load ALL available content until no more buttons found")
        else:
            print(f"🔄 Starting LIMITED LOAD MORE process...")
            print(f"📋 Will click 'More results' button maximum {max_load_more_clicks} times")

        start_time = time.time()
        load_more_clicks = 0
        consecutive_no_change = 0
        max_consecutive_no_change = 5  # Increased for more patience

        # Simple tracking for "More results" button only
        more_results_clicks = 0
        max_ineffective_clicks = 3  # Stop after 3 clicks without new content

        # Check if we have a click limit
        has_click_limit = max_load_more_clicks is not None
        if has_click_limit:
            print(f"🎯 Click limit: {max_load_more_clicks} 'More results' clicks maximum")

        # Initial state
        last_height = self.wd.execute_script("return document.body.scrollHeight")
        last_image_count = len(self._get_current_image_elements())

        print(f"📊 Initial state: {last_image_count} images, page height: {last_height}px")

        while (time.time() - start_time) < timeout_seconds:
            cycle_start = time.time()

            # Step 1: Scroll to bottom to trigger any lazy loading
            self._scroll_to_bottom_gradually()

            # Step 2: Check if we've reached the click limit
            if has_click_limit and load_more_clicks >= max_load_more_clicks:
                print(f"🎯 Reached click limit: {max_load_more_clicks} 'More results' clicks completed")
                print("🏁 Load More process complete - click limit reached!")
                break

            # Step 3: Simple "More results" button detection and clicking
            more_results_found = self._find_and_click_more_results_button()

            if more_results_found:
                load_more_clicks += 1
                more_results_clicks += 1
                print(f"✅ 'More results' clicked #{load_more_clicks}/{max_load_more_clicks if has_click_limit else '∞'} - Waiting for new content...")

                # Wait for content to load with progress monitoring
                self._wait_for_content_loading()

                # Reset no-change counter since we found and clicked a button
                consecutive_no_change = 0
            else:
                print("🔍 No 'More results' button found in this cycle")

            # Step 3: Check for content changes
            new_height = self.wd.execute_script("return document.body.scrollHeight")
            current_image_count = len(self._get_current_image_elements())

            # Progress feedback
            height_change = new_height - last_height
            image_change = current_image_count - last_image_count

            print(f"📈 Progress: Height {last_height}→{new_height} (+{height_change}), Images {last_image_count}→{current_image_count} (+{image_change})")

            # Step 4: Determine if we should continue
            if new_height == last_height and current_image_count == last_image_count:
                consecutive_no_change += 1
                print(f"⏸️ No changes detected ({consecutive_no_change}/{max_consecutive_no_change})")

                # If we just clicked "More results" but got no new content
                if more_results_found:
                    print(f"⚠️ 'More results' clicked but no new content loaded")
                    # If "More results" button clicked multiple times without effect, stop
                    if consecutive_no_change >= max_ineffective_clicks:
                        print("🛑 'More results' button appears to be ineffective - stopping")
                        break

                if consecutive_no_change >= max_consecutive_no_change:
                    print("🏁 No more content available - Load More process complete!")
                    break
            else:
                consecutive_no_change = 0  # Reset counter
                print("✅ New content detected, continuing...")

                # Reset ineffective click counter when new content is successfully loaded
                if more_results_found:
                    more_results_clicks = 0  # Reset counter for successful load

            # Update state for next iteration
            last_height = new_height
            last_image_count = current_image_count

            # Cycle timing info
            cycle_time = time.time() - cycle_start
            print(f"⏱️ Cycle completed in {cycle_time:.1f}s")

            # Brief pause between cycles
            time.sleep(2)

        # Final summary
        total_time = time.time() - start_time
        final_image_count = len(self._get_current_image_elements())

        print("=" * 60)
        print("🎊 CONTINUOUS LOAD MORE COMPLETED!")
        print(f"⏱️ Total time: {total_time:.1f} seconds")
        print(f"🔄 Load More clicks: {load_more_clicks}")
        print(f"📸 Final image count: {final_image_count}")
        print(f"📈 Images loaded: {final_image_count - (last_image_count if 'last_image_count' in locals() else 0)}")
        print("=" * 60)

    def _scroll_to_bottom_gradually(self):
        """Gradually scroll to bottom to trigger lazy loading"""
        try:
            # Get current scroll position and total height
            current_scroll = self.wd.execute_script("return window.pageYOffset")
            total_height = self.wd.execute_script("return document.body.scrollHeight")

            # If not at bottom, scroll gradually
            if current_scroll < total_height - 1000:  # 1000px buffer
                # Scroll in steps to trigger lazy loading
                steps = 3
                step_size = (total_height - current_scroll) // steps

                for i in range(steps):
                    scroll_to = current_scroll + (step_size * (i + 1))
                    self.wd.execute_script(f"window.scrollTo(0, {scroll_to});")
                    time.sleep(1)  # Wait for lazy loading

                # Final scroll to absolute bottom
                self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)

        except Exception as e:
            print(f"⚠️ Error in gradual scroll: {e}")
            # Fallback to simple scroll
            self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

    def _continuous_load_more_detection(self):
        """Continuously detect and click Load More buttons with multiple strategies"""
        max_detection_attempts = 3

        for attempt in range(max_detection_attempts):
            print(f"🔍 Load More detection attempt {attempt + 1}/{max_detection_attempts}")

            # Strategy 1: Enhanced button detection
            if self._enhanced_load_more_detection():
                return True

            # Strategy 2: Scroll-triggered detection (sometimes buttons appear after scroll)
            if attempt < max_detection_attempts - 1:  # Don't scroll on last attempt
                self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)

                if self._enhanced_load_more_detection():
                    return True

            # Strategy 3: Wait and retry (for dynamic content)
            if attempt < max_detection_attempts - 1:
                print(f"⏳ Waiting 3s before retry...")
                time.sleep(3)

        return False

    def _continuous_load_more_detection_with_tracking(self, clicked_buttons, max_same_button_clicks, button_effectiveness):
        """Enhanced Load More detection with button click tracking to prevent infinite loops"""
        max_detection_attempts = 3

        for attempt in range(max_detection_attempts):
            print(f"🔍 Load More detection attempt {attempt + 1}/{max_detection_attempts}")

            # Strategy 1: Enhanced button detection with tracking
            result = self._enhanced_load_more_detection_with_tracking(clicked_buttons, max_same_button_clicks, button_effectiveness)
            if result['found'] or result['reason'] in ['max_clicks_reached', 'ineffective_button']:
                return result

            # Strategy 2: Scroll-triggered detection (sometimes buttons appear after scroll)
            if attempt < max_detection_attempts - 1:  # Don't scroll on last attempt
                self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
                time.sleep(2)

                result = self._enhanced_load_more_detection_with_tracking(clicked_buttons, max_same_button_clicks, button_effectiveness)
                if result['found'] or result['reason'] in ['max_clicks_reached', 'ineffective_button']:
                    return result

            # Strategy 3: Wait and retry (for dynamic content)
            if attempt < max_detection_attempts - 1:
                print(f"⏳ Waiting 2s before retry...")  # Reduced wait time
                time.sleep(2)

        return {'found': False, 'reason': 'not_found', 'button_text': None}

    def _enhanced_load_more_detection(self):
        """Enhanced Load More button detection with multiple selectors and strategies"""
        # Comprehensive list of Load More button selectors
        load_more_selectors = [
            # eBird specific selectors
            'button[data-testid="load-more"]',
            'button[data-testid="load-more-button"]',
            'button[data-testid="show-more"]',
            '[data-testid*="load"]',
            '[data-testid*="more"]',

            # Generic Load More patterns
            'button:contains("Load More")',
            'button:contains("Show More")',
            'button:contains("Load more")',
            'button:contains("Show more")',
            'button:contains("More")',
            'button:contains("See More")',
            'button:contains("View More")',

            # Class-based selectors
            '.load-more-button',
            '.show-more-button',
            '.load-more',
            '.show-more',
            'button[class*="load"]',
            'button[class*="more"]',
            'button[class*="Load"]',
            'button[class*="More"]',

            # Aria labels and accessibility
            'button[aria-label*="more"]',
            'button[aria-label*="load"]',
            'button[aria-label*="More"]',
            'button[aria-label*="Load"]',
            '[role="button"][aria-label*="more"]',
            '[role="button"][aria-label*="load"]',

            # Pagination and navigation
            'a[href*="offset"]',
            'a[href*="page"]',
            '.pagination a:last-child',
            '.pagination button:last-child',
            'button[data-page]',

            # Generic button patterns that might be Load More
            'button[type="button"]:contains("more")',
            'button[type="button"]:contains("load")',
            'div[role="button"]:contains("more")',
            'div[role="button"]:contains("load")'
        ]

        for selector in load_more_selectors:
            try:
                if self._try_selector_for_load_more(selector):
                    return True
            except Exception as e:
                continue

        return False

    def _enhanced_load_more_detection_with_tracking(self, clicked_buttons, max_same_button_clicks, button_effectiveness):
        """Simple Load More button detection focused only on 'More results' button"""
        # Focus only on "More results" button - the one that actually works
        target_selectors = [
            'button:contains("More results")',  # Primary target
            'button:contains("More")',          # Fallback
        ]

        for selector in target_selectors:
            try:
                result = self._try_selector_for_load_more_with_tracking(selector, clicked_buttons, max_same_button_clicks, button_effectiveness)
                if result['found']:
                    return result
                elif result['reason'] in ['max_clicks_reached', 'ineffective_button']:
                    # If "More results" button is ineffective, stop completely
                    if "More results" in result['button_text']:
                        print(f"🛑 'More results' button is no longer effective - stopping")
                        return result
            except Exception as e:
                continue

        return {'found': False, 'reason': 'not_found', 'button_text': None}

    def _find_and_click_more_results_button(self):
        """Simple method to find and click only 'More results' button"""
        try:
            # Look for buttons containing "More results" text
            buttons = self.wd.find_elements(By.TAG_NAME, 'button')

            for button in buttons:
                try:
                    button_text = button.text.strip().lower()

                    # Only click if it's exactly "More results" or "More"
                    if "more results" in button_text or button_text == "more":
                        # Check if button is clickable
                        if button.is_displayed() and button.is_enabled():
                            print(f"✅ Found target button: '{button.text.strip()}'")

                            # Scroll to button and click
                            self.wd.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", button)
                            time.sleep(1)

                            try:
                                button.click()
                                print("✅ 'More results' clicked successfully")
                                return True
                            except Exception as click_error:
                                # Try JavaScript click as fallback
                                self.wd.execute_script("arguments[0].click();", button)
                                print("✅ 'More results' clicked successfully (JavaScript)")
                                return True

                except Exception as e:
                    continue

            print("🔍 No 'More results' button found")
            return False

        except Exception as e:
            print(f"❌ Error finding 'More results' button: {e}")
            return False

    def _try_selector_for_load_more(self, selector):
        """Try a specific selector to find and click Load More button"""
        try:
            # Handle :contains() pseudo-selector manually
            if ':contains(' in selector:
                return self._handle_contains_selector(selector)
            else:
                return self._handle_css_selector(selector)
        except Exception as e:
            return False

    def _try_selector_for_load_more_with_tracking(self, selector, clicked_buttons, max_same_button_clicks, button_effectiveness):
        """Try a specific selector to find and click Load More button with tracking"""
        try:
            # Handle :contains() pseudo-selector manually
            if ':contains(' in selector:
                return self._handle_contains_selector_with_tracking(selector, clicked_buttons, max_same_button_clicks, button_effectiveness)
            else:
                return self._handle_css_selector_with_tracking(selector, clicked_buttons, max_same_button_clicks, button_effectiveness)
        except Exception as e:
            return {'found': False, 'reason': 'error', 'button_text': None}

    def _handle_contains_selector_with_tracking(self, selector, clicked_buttons, max_same_button_clicks, button_effectiveness):
        """Handle selectors with :contains() pseudo-selector with tracking"""
        try:
            # Extract text to find and base selector
            parts = selector.split(':contains("')
            base_selector = parts[0]
            text_to_find = parts[1].split('")')[0]

            # Find elements by base selector
            if base_selector:
                elements = self.wd.find_elements(By.CSS_SELECTOR, base_selector)
            else:
                elements = self.wd.find_elements(By.TAG_NAME, 'button')

            for element in elements:
                if (text_to_find.lower() in element.text.lower() and
                    self._is_valid_load_more_button(element)):

                    button_text = element.text.strip()

                    # Check if this button is known to be ineffective
                    if button_text in button_effectiveness and button_effectiveness[button_text] == False:
                        print(f"⚠️ Button '{button_text}' is known to be ineffective, skipping")
                        return {'found': False, 'reason': 'ineffective_button', 'button_text': button_text}

                    # Check if this button has been clicked too many times
                    if button_text in clicked_buttons:
                        if clicked_buttons[button_text] >= max_same_button_clicks:
                            print(f"⚠️ Button '{button_text}' already clicked {clicked_buttons[button_text]} times, skipping")
                            button_effectiveness[button_text] = False  # Mark as ineffective
                            return {'found': False, 'reason': 'max_clicks_reached', 'button_text': button_text}

                    print(f"✅ Found Load More button: '{button_text}' (selector: {selector})")

                    # Try to click the button
                    if self._click_load_more_element(element):
                        # Track the click
                        clicked_buttons[button_text] = clicked_buttons.get(button_text, 0) + 1
                        return {'found': True, 'reason': 'clicked', 'button_text': button_text}

            return {'found': False, 'reason': 'not_found', 'button_text': None}

        except Exception as e:
            return {'found': False, 'reason': 'error', 'button_text': None}

    def _handle_css_selector_with_tracking(self, selector, clicked_buttons, max_same_button_clicks, button_effectiveness):
        """Handle regular CSS selectors with tracking"""
        try:
            elements = self.wd.find_elements(By.CSS_SELECTOR, selector)

            for element in elements:
                if self._is_valid_load_more_button(element):
                    button_text = element.text.strip() or element.get_attribute('aria-label') or 'Load More'

                    # Check if this button is known to be ineffective
                    if button_text in button_effectiveness and button_effectiveness[button_text] == False:
                        print(f"⚠️ Button '{button_text}' is known to be ineffective, skipping")
                        return {'found': False, 'reason': 'ineffective_button', 'button_text': button_text}

                    # Check if this button has been clicked too many times
                    if button_text in clicked_buttons:
                        if clicked_buttons[button_text] >= max_same_button_clicks:
                            print(f"⚠️ Button '{button_text}' already clicked {clicked_buttons[button_text]} times, skipping")
                            button_effectiveness[button_text] = False  # Mark as ineffective
                            return {'found': False, 'reason': 'max_clicks_reached', 'button_text': button_text}

                    print(f"✅ Found Load More element: '{button_text}' (selector: {selector})")

                    # Try to click the button
                    if self._click_load_more_element(element):
                        # Track the click
                        clicked_buttons[button_text] = clicked_buttons.get(button_text, 0) + 1
                        return {'found': True, 'reason': 'clicked', 'button_text': button_text}

            return {'found': False, 'reason': 'not_found', 'button_text': None}

        except Exception as e:
            return {'found': False, 'reason': 'error', 'button_text': None}

    def _handle_contains_selector(self, selector):
        """Handle selectors with :contains() pseudo-selector"""
        try:
            # Extract text to find and base selector
            parts = selector.split(':contains("')
            base_selector = parts[0]
            text_to_find = parts[1].split('")')[0]

            # Find elements by base selector
            if base_selector:
                elements = self.wd.find_elements(By.CSS_SELECTOR, base_selector)
            else:
                elements = self.wd.find_elements(By.TAG_NAME, 'button')

            for element in elements:
                if (text_to_find.lower() in element.text.lower() and
                    self._is_valid_load_more_button(element)):

                    print(f"✅ Found Load More button: '{element.text.strip()}' (selector: {selector})")
                    return self._click_load_more_element(element)

            return False

        except Exception as e:
            return False

    def _handle_css_selector(self, selector):
        """Handle regular CSS selectors"""
        try:
            elements = self.wd.find_elements(By.CSS_SELECTOR, selector)

            for element in elements:
                if self._is_valid_load_more_button(element):
                    button_text = element.text.strip() or element.get_attribute('aria-label') or 'Load More'
                    print(f"✅ Found Load More element: '{button_text}' (selector: {selector})")
                    return self._click_load_more_element(element)

            return False

        except Exception as e:
            return False

    def _is_valid_load_more_button(self, element):
        """Validate if an element is a valid Load More button"""
        try:
            # Must be displayed and enabled
            if not (element.is_displayed() and element.is_enabled()):
                return False

            # Check if element is in viewport or can be scrolled to
            try:
                location = element.location
                size = element.size
                if location['y'] < -1000 or location['y'] > 10000:  # Reasonable bounds
                    return False
            except:
                pass

            # Additional validation for text content
            text = element.text.lower()
            aria_label = (element.get_attribute('aria-label') or '').lower()

            # Must contain relevant keywords
            relevant_keywords = ['more', 'load', 'show', 'next', 'continue']
            has_relevant_text = any(keyword in text or keyword in aria_label
                                  for keyword in relevant_keywords)

            # Exclude irrelevant buttons
            exclude_keywords = ['less', 'hide', 'close', 'back', 'previous', 'cancel']
            has_exclude_text = any(keyword in text or keyword in aria_label
                                 for keyword in exclude_keywords)

            # Only accept "More results" or "More" buttons - be very specific
            if "more results" in text.lower():
                return True  # This is exactly what we want
            elif text.lower().strip() == "more":
                return True  # Simple "More" button is also acceptable
            else:
                # Reject everything else to avoid clicking wrong buttons
                print(f"⚠️ Skipping non-target button: '{text.strip()}'")
                return False

        except Exception as e:
            return False

    def _click_load_more_element(self, element):
        """Click a Load More element with multiple strategies"""
        try:
            # Strategy 1: Scroll element into view
            self.wd.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", element)
            time.sleep(1)

            # Strategy 2: Try regular click
            try:
                element.click()
                print("✅ Load More clicked successfully (regular click)")
                return True
            except Exception as click_error:
                print(f"⚠️ Regular click failed: {click_error}")

            # Strategy 3: Try JavaScript click
            try:
                self.wd.execute_script("arguments[0].click();", element)
                print("✅ Load More clicked successfully (JavaScript click)")
                return True
            except Exception as js_error:
                print(f"⚠️ JavaScript click failed: {js_error}")

            # Strategy 4: Try WebDriverWait + click
            try:
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC

                WebDriverWait(self.wd, 5).until(EC.element_to_be_clickable(element))
                element.click()
                print("✅ Load More clicked successfully (WebDriverWait click)")
                return True
            except Exception as wait_error:
                print(f"⚠️ WebDriverWait click failed: {wait_error}")

            return False

        except Exception as e:
            print(f"❌ All click strategies failed: {e}")
            return False

    def _wait_for_content_loading(self):
        """Wait for content to load after clicking Load More with progress monitoring"""
        print("⏳ Monitoring content loading...")

        initial_height = self.wd.execute_script("return document.body.scrollHeight")
        initial_images = len(self._get_current_image_elements())

        max_wait_time = 10  # Reduced wait time to prevent hanging on ineffective buttons
        check_interval = 1   # Check every second
        stable_count = 0     # Count of stable checks
        required_stable = 3  # Required stable checks before considering loaded

        for i in range(max_wait_time):
            time.sleep(check_interval)

            current_height = self.wd.execute_script("return document.body.scrollHeight")
            current_images = len(self._get_current_image_elements())

            height_change = current_height - initial_height
            image_change = current_images - initial_images

            if height_change > 0 or image_change > 0:
                print(f"📈 Loading progress: +{height_change}px height, +{image_change} images")
                stable_count = 0  # Reset stable count
                initial_height = current_height  # Update baseline
                initial_images = current_images
            else:
                stable_count += 1
                if stable_count >= required_stable:
                    print(f"✅ Content loading complete (stable for {stable_count}s)")
                    break

        # Final scroll to ensure all content is visible
        self.wd.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(1)

    def _get_current_image_elements(self):
        """Get current count of image elements on the page"""
        try:
            selectors = [
                'img[src*="macaulaylibrary.org"]',
                'img[src*="cdn.download.ams.birds.cornell.edu"]',
                'img[data-src*="macaulaylibrary.org"]',
                'img[data-src*="cdn.download.ams.birds.cornell.edu"]',
                '.MediaCard img',
                '.media-card img',
                '.photo-card img'
            ]

            all_images = []
            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    all_images.extend(images)
                except:
                    continue

            # Remove duplicates based on src attribute
            unique_images = []
            seen_srcs = set()
            for img in all_images:
                src = img.get_attribute('src') or img.get_attribute('data-src')
                if src and src not in seen_srcs:
                    unique_images.append(img)
                    seen_srcs.add(src)

            return unique_images
        except Exception as e:
            print(f"Error getting current image elements: {e}")
            return []

    def _click_load_more_button(self):
        """Legacy method - now uses enhanced load more detection"""
        print("🔄 Using enhanced Load More detection...")
        return self._enhanced_load_more_detection()

    def _get_clickable_images_from_page(self):
        """Cari semua gambar yang bisa diklik untuk melihat versi penuh"""
        clickable_images = []

        try:
            # Tunggu sampai halaman dimuat
            time.sleep(3)

            # Cari elemen gambar yang bisa diklik (biasanya dalam link atau card)
            selectors = [
                "a[href*='/catalog/'] img",  # Gambar dalam link catalog
                ".MediaCard img",            # Gambar dalam MediaCard
                ".MediaThumbnail img",       # Gambar thumbnail
                "[data-testid='media-card'] img",  # Gambar dalam media card
                "img[src*='macaulaylibrary.org']",  # Gambar langsung
                "img[src*='cdn.download.ams.birds.cornell.edu']"  # Gambar CDN
            ]

            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    print(f"Found {len(images)} clickable images with selector: {selector}")

                    for img in images:
                        # Cek apakah gambar bisa diklik (ada parent link atau clickable)
                        parent_link = None
                        try:
                            # Cari parent link
                            parent_link = img.find_element(By.XPATH, "./ancestor::a[1]")
                        except:
                            # Jika tidak ada parent link, coba klik gambar langsung
                            pass

                        clickable_images.append({
                            'element': img,
                            'parent_link': parent_link,
                            'src': img.get_attribute('src') or img.get_attribute('data-src')
                        })

                except Exception as e:
                    print(f"Error with selector {selector}: {e}")
                    continue

            print(f"Total clickable images found: {len(clickable_images)}")
            return clickable_images

        except Exception as e:
            print(f"Error finding clickable images: {e}")
            return []

    def _click_and_get_full_image(self, clickable_img, index, output_dir=None, download_method='url', crop_to_bird=False):
        """Simplified click and get full image - Try URL first, fallback to screenshot"""
        max_retries = 3
        retry_count = 0

        while retry_count < max_retries:
            try:
                print(f"🖱️ Clicking image {index + 1} (attempt {retry_count + 1}/{max_retries})")

                # Scroll element into view with better positioning
                self.wd.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'center'
                    });
                """, clickable_img['element'])
                time.sleep(2)

                # Wait for element to be clickable and click it
                from selenium.webdriver.support.ui import WebDriverWait
                from selenium.webdriver.support import expected_conditions as EC

                try:
                    if clickable_img['parent_link']:
                        print(f"🔗 Clicking parent link for image {index + 1}")
                        WebDriverWait(self.wd, 10).until(
                            EC.element_to_be_clickable(clickable_img['parent_link'])
                        )
                        clickable_img['parent_link'].click()
                    else:
                        print(f"🖼️ Clicking image {index + 1} directly")
                        WebDriverWait(self.wd, 10).until(
                            EC.element_to_be_clickable(clickable_img['element'])
                        )
                        clickable_img['element'].click()
                except Exception as click_error:
                    print(f"⚠️ Click failed, trying JavaScript click: {click_error}")
                    # Fallback to JavaScript click
                    element_to_click = clickable_img['parent_link'] or clickable_img['element']
                    self.wd.execute_script("arguments[0].click();", element_to_click)

                # Wait for image viewer to load
                print(f"⏳ Waiting for image viewer to load...")
                time.sleep(4)  # Simple wait instead of complex verification

                result = None

                # STEP 1: Try URL download first (no ID validation barriers)
                if download_method == 'url':
                    print(f"🔗 Attempting URL download for image {index + 1}...")
                    result = self._try_url_download(index, output_dir, crop_to_bird)

                    if result:
                        print(f"✅ URL download successful: {os.path.basename(result)}")
                    else:
                        print(f"⚠️ URL download failed, will try screenshot fallback")

                # STEP 2: Fallback to screenshot if URL download failed or not requested
                if not result:
                    print(f"📸 Taking screenshot for image {index + 1}...")
                    result = self._try_screenshot_capture(index, output_dir)

                    if result:
                        print(f"✅ Screenshot successful: {os.path.basename(result)}")
                    else:
                        print(f"❌ Screenshot also failed for image {index + 1}")

                # STEP 3: Always return to gallery (regardless of success/failure)
                print(f"🔄 Returning to gallery after processing image {index + 1}...")
                navigation_success = self._return_to_main_page_with_context(index)

                if result:
                    if navigation_success:
                        print(f"✅ Successfully processed and returned for image {index + 1}")
                    else:
                        print(f"⚠️ Image processed but navigation back failed for image {index + 1}")
                    return result
                else:
                    print(f"❌ Failed to capture image {index + 1} (tried both URL and screenshot)")
                    return None

            except Exception as e:
                retry_count += 1
                error_msg = f"Error processing image {index + 1} (attempt {retry_count}): {e}"
                print(f"⚠️ {error_msg}")

                # Always try to return to main page after error
                try:
                    self._return_to_main_page_with_context(index)
                except:
                    pass

                if retry_count < max_retries:
                    print(f"🔄 Retrying in 3 seconds...")
                    time.sleep(3)
                else:
                    print(f"❌ All attempts failed for image {index + 1}")
                    return None

        return None

    def _try_url_download(self, index, output_dir, crop_to_bird=False):
        """Try to download image from URL without ID validation barriers"""
        try:
            # Wait a moment for the image viewer to load
            time.sleep(3)

            # Look for the full resolution image URL in the current page (no ID validation)
            image_url = self._find_full_resolution_image_fast()

            if not image_url:
                print(f"   ❌ No image URL found for image {index + 1}")
                return None

            print(f"   🔗 Found image URL: {image_url[:80]}...")

            # Generate simple filename without asset ID dependency
            filename = f"ebird_image_{index + 1:03d}.jpg"

            # Attempt download (no duplicate checking)
            download_success = self._download_image(image_url, filename, output_dir)

            if download_success:
                filepath = os.path.join(output_dir, filename)
                print(f"   ✅ URL download successful")

                # Apply cropping if requested
                if crop_to_bird:
                    print(f"   ✂️ Applying cropping to downloaded image...")
                    try:
                        cropped_path = self._crop_downloaded_image(filepath)
                        if cropped_path:
                            print(f"   ✅ Image cropped successfully")
                            return cropped_path
                        else:
                            print(f"   ⚠️ Cropping failed, keeping original")
                            return filepath
                    except Exception as crop_error:
                        print(f"   ⚠️ Cropping error, keeping original: {crop_error}")
                        return filepath
                else:
                    return filepath
            else:
                print(f"   ❌ URL download failed")
                return None

        except Exception as e:
            print(f"   ❌ URL download error for image {index + 1}: {e}")
            return None

    def _try_screenshot_capture(self, index, output_dir):
        """Try to capture image via screenshot"""
        try:
            # Generate filename
            filename = f"ebird_screenshot_{index + 1:03d}.png"
            filepath = os.path.join(output_dir, filename)

            # Take screenshot of current page
            self.wd.save_screenshot(filepath)

            if os.path.exists(filepath):
                print(f"   📸 Screenshot saved: {filename}")

                # Try to crop to bird image if possible
                try:
                    cropped_path = self._crop_screenshot_to_bird_image(filepath)
                    if cropped_path and cropped_path != filepath:
                        return cropped_path
                except Exception as crop_error:
                    print(f"   ⚠️ Cropping failed, keeping full screenshot: {crop_error}")

                return filepath
            else:
                print(f"   ❌ Screenshot file not created: {filename}")
                return None

        except Exception as e:
            print(f"   ❌ Screenshot error for image {index + 1}: {e}")
            return None

    def _click_and_screenshot_image(self, clickable_img, index, output_dir, crop_to_bird=True):
        """Click image and take screenshot - simplified method focused on screenshots"""
        try:
            print(f"🖱️ Clicking image {index + 1} for screenshot...")

            # Click the image to open full view
            success = self._click_image_element(clickable_img, index)
            if not success:
                print(f"❌ Failed to click image {index + 1}")
                return None

            # Wait for image to load
            print(f"⏳ Waiting for full image to load...")
            time.sleep(4)

            # Take screenshot of the full image viewer
            screenshot_path = self._take_full_image_screenshot(index + 1, output_dir, crop_to_bird)

            if screenshot_path:
                print(f"📸 Screenshot taken: {screenshot_path}")

                # Return to gallery
                print(f"🔄 Returning to gallery...")
                self._return_to_main_page_with_context(index)

                return screenshot_path
            else:
                print(f"❌ Failed to take screenshot for image {index + 1}")
                return None

        except Exception as e:
            print(f"❌ Error processing image {index + 1}: {e}")
            return None

    def _click_image_element(self, clickable_img, index):
        """Click the image element to open full view"""
        try:
            # Extract WebElement from dictionary
            img_element = clickable_img['element']
            parent_link = clickable_img.get('parent_link')

            # Scroll to image
            self.wd.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", img_element)
            time.sleep(1)

            # Try clicking parent link first (more reliable)
            if parent_link:
                try:
                    parent_link.click()
                    print(f"✅ Parent link clicked for image {index + 1}")
                    return True
                except Exception as parent_error:
                    print(f"⚠️ Parent link click failed: {parent_error}")

            # Try clicking the image directly
            try:
                img_element.click()
                print(f"✅ Image {index + 1} clicked successfully")
                return True

            except Exception as direct_click_error:
                print(f"⚠️ Direct click failed, trying JavaScript click...")

                # Try JavaScript click as fallback
                element_to_click = parent_link if parent_link else img_element
                self.wd.execute_script("arguments[0].click();", element_to_click)
                print(f"✅ JavaScript click successful for image {index + 1}")
                return True

        except Exception as e:
            print(f"❌ Failed to click image {index + 1}: {e}")
            return False

    def _take_full_image_screenshot(self, image_number, output_dir, crop_to_bird=True):
        """Take screenshot of the full image viewer and optionally crop to bird image"""
        try:
            # Wait a bit more for image to fully load
            time.sleep(2)

            # Generate filename
            filename = f"ebird_screenshot_{image_number:03d}.png"
            filepath = os.path.join(output_dir, filename)

            # Take screenshot
            self.wd.save_screenshot(filepath)

            if os.path.exists(filepath):
                print(f"✅ Screenshot saved: {filename}")

                # Crop to bird image if requested
                if crop_to_bird:
                    cropped_path = self._crop_screenshot_to_bird_image(filepath)
                    return cropped_path
                else:
                    return filepath
            else:
                print(f"❌ Screenshot file not created: {filename}")
                return None

        except Exception as e:
            print(f"❌ Error taking screenshot: {e}")
            return None

    def _wait_for_new_image_and_get_url(self, clicked_img_src, index):
        """Wait for UNIQUE image viewer to load and get URL with duplicate detection"""
        try:
            print(f"🔍 Waiting for UNIQUE image viewer to load (image {index + 1})...")

            max_wait_attempts = 15  # 15 seconds max wait
            wait_interval = 1
            last_detected_asset_id = None

            for attempt in range(max_wait_attempts):
                time.sleep(wait_interval)

                # Check if we're now in a modal/viewer state
                if self._is_in_image_viewer():
                    print(f"✅ Image viewer detected (attempt {attempt + 1})")

                    # Fast asset ID detection
                    current_asset_id = self._get_current_image_asset_id()

                    if current_asset_id:
                        # Check if this is a unique asset ID
                        if self._is_asset_id_unique(current_asset_id):
                            print(f"✅ UNIQUE asset ID found: {current_asset_id}")

                            # Get the full resolution URL
                            full_image_url = self._find_full_resolution_image_fast()

                            if full_image_url:
                                # Verify the URL contains the same asset ID
                                url_asset_id = self._extract_asset_id_fast(full_image_url)
                                if url_asset_id == current_asset_id:
                                    print(f"✅ URL verified with asset ID: {current_asset_id}")
                                    return full_image_url
                                else:
                                    print(f"⚠️ URL asset ID mismatch: {url_asset_id} vs {current_asset_id}")
                            else:
                                print(f"⚠️ Could not get full resolution URL for asset {current_asset_id}")
                        else:
                            print(f"⚠️ Duplicate asset ID detected: {current_asset_id} (attempt {attempt + 1})")
                            last_detected_asset_id = current_asset_id

                            # If we keep getting the same duplicate, try to refresh
                            if attempt > 5:
                                print(f"🔄 Same duplicate detected multiple times, forcing refresh...")
                                self._clear_browser_cache_and_refresh()
                                time.sleep(2)
                    else:
                        print(f"⏳ No asset ID detected yet (attempt {attempt + 1})")
                else:
                    print(f"⏳ Waiting for image viewer to load... (attempt {attempt + 1})")

                # Enhanced refresh every 5 attempts
                if attempt > 0 and attempt % 5 == 0:
                    print(f"🔄 Enhanced refresh (attempt {attempt + 1})")
                    self._clear_browser_cache_and_refresh()
                    time.sleep(2)

            # Final check - if we detected a duplicate consistently, report it
            if last_detected_asset_id:
                print(f"❌ Timeout: Only duplicate asset ID {last_detected_asset_id} detected")
                self._current_session_stats['failed_detections'] += 1
            else:
                print(f"❌ Timeout: No asset ID detected at all")
                self._current_session_stats['failed_detections'] += 1

            return None

        except Exception as e:
            print(f"❌ Error waiting for unique image: {e}")
            self._current_session_stats['failed_detections'] += 1
            return None

    def _is_different_image(self, full_url, thumbnail_src):
        """Check if the full resolution URL is different from the thumbnail source"""
        try:
            if not full_url or not thumbnail_src:
                return True  # If we can't compare, assume it's different

            # Extract asset ID from URLs for comparison
            import re

            # Extract asset ID from full URL
            full_match = re.search(r'/asset/(\d+)/', full_url)
            full_asset_id = full_match.group(1) if full_match else None

            # Extract asset ID from thumbnail URL
            thumb_match = re.search(r'/asset/(\d+)/', thumbnail_src)
            thumb_asset_id = thumb_match.group(1) if thumb_match else None

            if full_asset_id and thumb_asset_id:
                is_different = full_asset_id != thumb_asset_id
                if is_different:
                    print(f"✅ Confirmed different image: {full_asset_id} vs {thumb_asset_id}")
                else:
                    print(f"⚠️ Same image detected: {full_asset_id} = {thumb_asset_id}")
                return is_different

            # Fallback: compare URLs directly
            return full_url != thumbnail_src

        except Exception as e:
            print(f"⚠️ Error comparing images, assuming different: {e}")
            return True

    def _is_in_image_viewer(self):
        """Check if we're currently in an image viewer/modal state (not in gallery view)"""
        try:
            # Check for common modal/viewer indicators
            viewer_selectors = [
                # Modal/lightbox containers
                ".modal:not([style*='display: none'])",
                ".lightbox:not([style*='display: none'])",
                ".fullscreen:not([style*='display: none'])",

                # eBird specific viewers
                ".MediaDisplay",
                ".MediaViewer",
                ".FullscreenImage",
                "[data-testid='media-display']",

                # Generic overlay/popup indicators
                ".overlay:not([style*='display: none'])",
                ".popup:not([style*='display: none'])",
                "[role='dialog']:not([style*='display: none'])",

                # Check for backdrop/overlay elements
                ".modal-backdrop",
                ".overlay-backdrop",

                # Look for close buttons (indicates modal is open)
                "button[aria-label*='close']:not([style*='display: none'])",
                "button[title*='close']:not([style*='display: none'])",
                ".close-button:not([style*='display: none'])"
            ]

            for selector in viewer_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            print(f"   ✅ Image viewer detected: {selector}")
                            return True
                except:
                    continue

            # Additional check: look for URL changes that indicate viewer mode
            current_url = self.wd.current_url
            if any(indicator in current_url.lower() for indicator in ['modal', 'viewer', 'fullscreen', 'asset']):
                print(f"   ✅ Image viewer detected via URL: {current_url}")
                return True

            # Check if page title changed (some sites change title in viewer mode)
            try:
                current_title = self.wd.title
                if any(indicator in current_title.lower() for indicator in ['photo', 'image', 'media']):
                    print(f"   ✅ Image viewer detected via title: {current_title}")
                    return True
            except:
                pass

            return False

        except Exception as e:
            print(f"❌ Error checking image viewer state: {e}")
            return False  # If error, assume we're not in viewer to avoid infinite loop

    def _clear_browser_cache_and_refresh(self):
        """Clear browser cache and refresh page state to prevent stale content detection"""
        try:
            print("   🧹 Clearing browser cache and refreshing...")

            # Method 1: Clear browser cache via DevTools (most effective)
            try:
                self.wd.execute_cdp_cmd('Network.clearBrowserCache', {})
                print("   ✅ Browser cache cleared via DevTools")
            except Exception as e:
                print(f"   ⚠️ DevTools cache clear failed: {e}")

            # Method 2: Clear browser cookies and local storage
            try:
                self.wd.delete_all_cookies()
                self.wd.execute_script("window.localStorage.clear();")
                self.wd.execute_script("window.sessionStorage.clear();")
                print("   ✅ Cookies and storage cleared")
            except Exception as e:
                print(f"   ⚠️ Storage clear failed: {e}")

            # Method 3: Force hard refresh
            try:
                self.wd.execute_script("location.reload(true);")  # Hard refresh
                time.sleep(2)
                print("   ✅ Hard refresh completed")
            except Exception as e:
                print(f"   ⚠️ Hard refresh failed: {e}")

            # Method 4: Additional DOM manipulation to force re-render
            try:
                self.wd.execute_script("""
                    // Force DOM re-render
                    document.body.style.display = 'none';
                    document.body.offsetHeight; // Trigger reflow
                    document.body.style.display = '';

                    // Force image cache invalidation
                    var images = document.querySelectorAll('img');
                    images.forEach(function(img) {
                        if (img.src) {
                            var src = img.src;
                            img.src = '';
                            img.src = src + (src.includes('?') ? '&' : '?') + '_t=' + Date.now();
                        }
                    });
                """)
                print("   ✅ DOM refresh and image cache invalidation completed")
            except Exception as e:
                print(f"   ⚠️ DOM refresh failed: {e}")

        except Exception as e:
            print(f"   ❌ Error during cache clearing: {e}")

    def _progressive_wait_for_viewer(self, index):
        """Progressive wait strategy: start fast, increase timeout if needed"""
        try:
            print(f"   🎯 Using progressive wait strategy for image {index + 1}...")

            # Phase 1: Quick check (0.5s intervals, 3 attempts = 1.5s total)
            print(f"   ⚡ Phase 1: Quick detection...")
            for attempt in range(3):
                time.sleep(0.5)
                if self._is_in_image_viewer():
                    print(f"   ✅ Quick detection successful (0.{5 * (attempt + 1)}s)")
                    return True

            # Phase 2: Medium wait (1s intervals, 4 attempts = 4s total)
            print(f"   ⏳ Phase 2: Medium wait...")
            for attempt in range(4):
                time.sleep(1)
                if self._is_in_image_viewer():
                    print(f"   ✅ Medium wait successful ({1.5 + attempt + 1}s)")
                    return True

            # Phase 3: Longer wait with DOM checks (2s intervals, 3 attempts = 6s total)
            print(f"   🔍 Phase 3: Deep detection with DOM analysis...")
            for attempt in range(3):
                time.sleep(2)

                # Force DOM update
                self.wd.execute_script("document.body.offsetHeight;")  # Trigger reflow

                if self._is_in_image_viewer():
                    print(f"   ✅ Deep detection successful ({5.5 + (attempt + 1) * 2}s)")
                    return True

                # Additional check: look for any significant DOM changes
                if self._detect_dom_changes():
                    print(f"   ✅ DOM changes detected, assuming viewer loaded")
                    return True

            print(f"   ❌ Progressive wait failed after ~11.5 seconds")
            return False

        except Exception as e:
            print(f"   ❌ Error in progressive wait: {e}")
            return False

    def _detect_dom_changes(self):
        """Detect if significant DOM changes occurred (indicating page state change)"""
        try:
            # Check for common indicators of page state changes
            indicators = [
                # Check if body class changed (many sites add modal classes)
                "document.body.className.includes('modal') || document.body.className.includes('overlay')",

                # Check for z-index changes (modals usually have high z-index)
                "Array.from(document.querySelectorAll('*')).some(el => window.getComputedStyle(el).zIndex > 1000)",

                # Check for backdrop elements
                "document.querySelector('.modal-backdrop, .overlay-backdrop, [role=\"dialog\"]') !== null",

                # Check for elements with high opacity that weren't there before
                "Array.from(document.querySelectorAll('*')).some(el => el.style.position === 'fixed' && window.getComputedStyle(el).zIndex > 100)"
            ]

            for indicator in indicators:
                try:
                    result = self.wd.execute_script(f"return {indicator};")
                    if result:
                        print(f"   ✅ DOM change detected: {indicator[:50]}...")
                        return True
                except:
                    continue

            return False

        except Exception as e:
            print(f"   ❌ Error detecting DOM changes: {e}")
            return False

    def _extract_asset_id_fast(self, url):
        """Fast asset ID extraction from URL"""
        try:
            if not url:
                return None

            import re
            # Extract asset ID from URL patterns
            patterns = [
                r'/asset/(\d+)/',
                r'asset_id=(\d+)',
                r'assetId=(\d+)',
                r'/(\d{8,})',  # 8+ digit numbers (common for asset IDs)
            ]

            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    asset_id = match.group(1)
                    return asset_id

            return None

        except Exception as e:
            print(f"   ❌ Error extracting asset ID: {e}")
            return None

    def _is_asset_id_unique(self, asset_id):
        """Check if asset ID is unique (not already downloaded)"""
        if not asset_id:
            return False

        is_unique = asset_id not in self._downloaded_asset_ids

        if not is_unique:
            print(f"   ⚠️ Duplicate asset ID detected: {asset_id}")
            self._current_session_stats['duplicates_skipped'] += 1

        return is_unique

    def _mark_asset_as_downloaded(self, asset_id):
        """Mark asset ID as downloaded"""
        if asset_id:
            self._downloaded_asset_ids.add(asset_id)
            self._current_session_stats['unique_downloads'] += 1
            print(f"   ✅ Asset ID {asset_id} marked as downloaded")

    def _get_current_image_asset_id(self):
        """Get asset ID of currently displayed image in viewer - FAST method"""
        try:
            print(f"   🔍 Fast asset ID detection...")

            # Method 1: Direct URL inspection from current page
            current_url = self.wd.current_url
            asset_id = self._extract_asset_id_fast(current_url)
            if asset_id:
                print(f"   ✅ Asset ID from URL: {asset_id}")
                return asset_id

            # Method 2: Fast DOM inspection for image URLs
            fast_selectors = [
                # High priority - main image in viewer
                ".MediaDisplay img[src*='macaulaylibrary.org']",
                ".MediaViewer img[src*='macaulaylibrary.org']",
                "[data-testid='media-display'] img[src*='macaulaylibrary.org']",

                # Fallback - any large visible image
                "img[src*='macaulaylibrary.org']:not([src*='thumbnail']):not([src*='small'])"
            ]

            for selector in fast_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            img_src = element.get_attribute('src')
                            if img_src:
                                asset_id = self._extract_asset_id_fast(img_src)
                                if asset_id:
                                    print(f"   ✅ Asset ID from DOM: {asset_id}")
                                    return asset_id
                except:
                    continue

            # Method 3: JavaScript execution for active image
            try:
                js_script = """
                // Find the most prominent image
                var images = document.querySelectorAll('img[src*="macaulaylibrary.org"]');
                var largestImg = null;
                var maxArea = 0;

                for (var i = 0; i < images.length; i++) {
                    var img = images[i];
                    if (img.offsetWidth > 0 && img.offsetHeight > 0) {
                        var area = img.offsetWidth * img.offsetHeight;
                        if (area > maxArea) {
                            maxArea = area;
                            largestImg = img;
                        }
                    }
                }

                return largestImg ? largestImg.src : null;
                """

                img_src = self.wd.execute_script(js_script)
                if img_src:
                    asset_id = self._extract_asset_id_fast(img_src)
                    if asset_id:
                        print(f"   ✅ Asset ID from JavaScript: {asset_id}")
                        return asset_id
            except Exception as js_error:
                print(f"   ⚠️ JavaScript asset detection failed: {js_error}")

            print(f"   ❌ No asset ID found")
            return None

        except Exception as e:
            print(f"   ❌ Error getting current image asset ID: {e}")
            return None

    def _find_full_resolution_image_fast(self):
        """Fast method to find full resolution image URL - optimized for speed"""
        try:
            print(f"🚀 Fast full resolution image detection...")

            # Method 1: Direct high-priority selectors (fastest)
            priority_selectors = [
                # eBird specific high-res patterns
                ".MediaDisplay img[src*='macaulaylibrary.org']:not([src*='thumbnail'])",
                ".MediaViewer img[src*='macaulaylibrary.org']:not([src*='small'])",
                "[data-testid='media-display'] img[src*='macaulaylibrary.org']",

                # High resolution indicators
                "img[src*='macaulaylibrary.org'][src*='original']",
                "img[src*='macaulaylibrary.org'][src*='full']",
                "img[src*='macaulaylibrary.org'][src*='2400']",
                "img[src*='macaulaylibrary.org'][src*='1920']"
            ]

            for selector in priority_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            img_url = element.get_attribute('src')
                            if img_url and self._is_high_quality_url(img_url):
                                print(f"✅ Fast detection successful: {img_url[:80]}...")
                                return self._convert_to_ultra_high_res(img_url)
                except:
                    continue

            # Method 2: JavaScript-based detection (medium speed)
            try:
                js_script = """
                // Find the largest, most prominent image
                var images = document.querySelectorAll('img[src*="macaulaylibrary.org"]');
                var bestImg = null;
                var bestScore = 0;

                for (var i = 0; i < images.length; i++) {
                    var img = images[i];
                    if (img.offsetWidth > 0 && img.offsetHeight > 0 && img.src) {
                        var score = 0;

                        // Size score
                        score += img.offsetWidth * img.offsetHeight / 1000;

                        // Quality indicators
                        if (img.src.includes('original')) score += 1000;
                        if (img.src.includes('full')) score += 800;
                        if (img.src.includes('2400') || img.src.includes('1920')) score += 600;

                        // Penalties
                        if (img.src.includes('thumbnail') || img.src.includes('small')) score -= 500;

                        if (score > bestScore) {
                            bestScore = score;
                            bestImg = img;
                        }
                    }
                }

                return bestImg ? bestImg.src : null;
                """

                best_url = self.wd.execute_script(js_script)
                if best_url and self._is_high_quality_url(best_url):
                    print(f"✅ JavaScript detection successful: {best_url[:80]}...")
                    return self._convert_to_ultra_high_res(best_url)

            except Exception as js_error:
                print(f"   ⚠️ JavaScript detection failed: {js_error}")

            # Method 3: Fallback to any visible eBird image
            try:
                fallback_elements = self.wd.find_elements(By.CSS_SELECTOR, "img[src*='macaulaylibrary.org']")
                for element in fallback_elements:
                    if element.is_displayed():
                        img_url = element.get_attribute('src')
                        if img_url:
                            print(f"⚠️ Fallback detection: {img_url[:80]}...")
                            return self._convert_to_ultra_high_res(img_url)
            except:
                pass

            print(f"❌ No full resolution image found")
            return None

        except Exception as e:
            print(f"❌ Error in fast full resolution detection: {e}")
            return None

    def _is_high_quality_url(self, url):
        """Quick check if URL indicates high quality image"""
        if not url:
            return False

        url_lower = url.lower()

        # High quality indicators
        quality_indicators = ['original', 'full', '2400', '1920', 'xlarge', 'large']
        has_quality = any(indicator in url_lower for indicator in quality_indicators)

        # Low quality penalties
        low_quality = any(indicator in url_lower for indicator in ['thumbnail', 'thumb', 'small', 'tiny'])

        return has_quality or not low_quality

    def _print_asset_tracking_stats(self):
        """Print comprehensive asset ID tracking statistics"""
        try:
            print("\n" + "="*60)
            print("📊 ASSET ID TRACKING STATISTICS")
            print("="*60)

            stats = self._current_session_stats
            print(f"🎯 Images Attempted: {stats['attempted']}")
            print(f"✅ Unique Downloads: {stats['unique_downloads']}")
            print(f"⚠️ Duplicates Skipped: {stats['duplicates_skipped']}")
            print(f"❌ Failed Detections: {stats['failed_detections']}")

            if stats['attempted'] > 0:
                success_rate = (stats['unique_downloads'] / stats['attempted']) * 100
                duplicate_rate = (stats['duplicates_skipped'] / stats['attempted']) * 100
                print(f"📈 Success Rate: {success_rate:.1f}%")
                print(f"📉 Duplicate Rate: {duplicate_rate:.1f}%")

            print(f"🗂️ Total Unique Assets Downloaded: {len(self._downloaded_asset_ids)}")

            if self._downloaded_asset_ids:
                print(f"📋 Downloaded Asset IDs:")
                for i, asset_id in enumerate(sorted(self._downloaded_asset_ids), 1):
                    print(f"   {i:2d}. {asset_id}")

            print("="*60)

        except Exception as e:
            print(f"❌ Error printing asset tracking stats: {e}")

    def _reset_session_stats(self):
        """Reset session statistics (keep downloaded asset IDs)"""
        self._current_session_stats = {
            'attempted': 0,
            'unique_downloads': 0,
            'duplicates_skipped': 0,
            'failed_detections': 0
        }
        print("🔄 Session statistics reset")

    def _clear_all_tracking(self):
        """Clear all tracking data (use with caution)"""
        self._downloaded_asset_ids.clear()
        self._reset_session_stats()
        print("🧹 All asset tracking data cleared")

    def _get_duplicate_prevention_summary(self):
        """Get summary of duplicate prevention effectiveness"""
        stats = self._current_session_stats
        total_processed = stats['unique_downloads'] + stats['duplicates_skipped']

        if total_processed == 0:
            return "No images processed yet"

        prevention_rate = (stats['duplicates_skipped'] / total_processed) * 100

        summary = f"Duplicate Prevention: {stats['duplicates_skipped']} duplicates prevented out of {total_processed} total detections ({prevention_rate:.1f}% prevention rate)"

        return summary

    def _handle_processing_error(self, image_index, error, stats):
        """Comprehensive error handling with circuit breaker pattern"""
        try:
            print(f"🚨 Handling processing error for image {image_index + 1}: {error}")

            # Initialize error tracking if not exists
            if not hasattr(self, '_error_tracker'):
                self._error_tracker = {
                    'consecutive_failures': 0,
                    'total_failures': 0,
                    'last_error_time': 0,
                    'error_types': {}
                }

            # Update error tracking
            self._error_tracker['consecutive_failures'] += 1
            self._error_tracker['total_failures'] += 1
            self._error_tracker['last_error_time'] = time.time()

            error_type = type(error).__name__
            self._error_tracker['error_types'][error_type] = self._error_tracker['error_types'].get(error_type, 0) + 1

            # Circuit breaker logic
            if self._error_tracker['consecutive_failures'] >= 3:
                print(f"🔴 Circuit breaker triggered: {self._error_tracker['consecutive_failures']} consecutive failures")

                # Check if we should stop processing
                if self._error_tracker['consecutive_failures'] >= 5:
                    print(f"❌ Too many consecutive failures ({self._error_tracker['consecutive_failures']}), stopping to prevent infinite loops")
                    return False

                # Enhanced recovery for circuit breaker state
                print(f"🔧 Attempting enhanced recovery...")
                recovery_success = self._enhanced_error_recovery()

                if recovery_success:
                    print(f"✅ Enhanced recovery successful, resetting circuit breaker")
                    self._error_tracker['consecutive_failures'] = 0
                    return True
                else:
                    print(f"❌ Enhanced recovery failed")
                    return False

            # Standard recovery for minor errors
            print(f"🔧 Attempting standard recovery...")
            recovery_success = self._standard_error_recovery()

            if recovery_success:
                print(f"✅ Standard recovery successful")
                return True
            else:
                print(f"⚠️ Standard recovery failed, error count: {self._error_tracker['consecutive_failures']}")
                return True  # Continue but with increased error count

        except Exception as recovery_error:
            print(f"❌ Error in error handling: {recovery_error}")
            return False

    def _enhanced_error_recovery(self):
        """Enhanced recovery for critical error states"""
        try:
            print(f"   🔧 Enhanced recovery: Full browser state reset...")

            # Step 1: Clear all browser state
            self._clear_browser_cache_and_refresh()
            time.sleep(3)

            # Step 2: Navigate back to original gallery
            if hasattr(self, '_original_gallery_url') and self._original_gallery_url:
                print(f"   🔄 Navigating back to original gallery...")
                self.wd.get(self._original_gallery_url)
                time.sleep(5)

            # Step 3: Wait for gallery to be ready
            if self._wait_for_gallery_ready(timeout=30):
                print(f"   ✅ Gallery ready after enhanced recovery")
                return True
            else:
                print(f"   ❌ Gallery not ready after enhanced recovery")
                return False

        except Exception as e:
            print(f"   ❌ Enhanced recovery failed: {e}")
            return False

    def _standard_error_recovery(self):
        """Standard recovery for minor errors"""
        try:
            print(f"   🔧 Standard recovery: Page state reset...")

            # Step 1: Try to return to gallery
            recovery_success = self._recover_page_state()
            if not recovery_success:
                print(f"   ⚠️ Page state recovery failed")
                return False

            # Step 2: Verify gallery is ready
            if self._verify_ready_for_next_image():
                print(f"   ✅ Gallery ready after standard recovery")
                return True
            else:
                print(f"   ⚠️ Gallery not ready after standard recovery")
                return False

        except Exception as e:
            print(f"   ❌ Standard recovery failed: {e}")
            return False

    def _return_to_main_page_with_context(self, image_index):
        """Context-aware navigation back to main page with specific handling for image processing"""
        print(f"🔄 Returning to main gallery after processing image {image_index + 1}...")

        try:
            # Store original URL before any navigation attempts
            original_url = getattr(self, '_original_gallery_url', None)

            # Step 1: Close any modals/overlays first
            print("   🔄 Closing modals/overlays...")
            self._close_modal_or_overlay()
            time.sleep(2)

            # Step 2: Check if we're already on the main page
            current_url = self.wd.current_url
            if self._is_gallery_page(current_url):
                print("   ✅ Already on gallery page")
                if self._verify_ready_for_next_image():
                    return True

            # Step 3: Try browser back button first (most reliable)
            print("   ⬅️ Using browser back button...")
            try:
                self.wd.back()
                time.sleep(3)

                # Check if back button worked
                if self._verify_ready_for_next_image():
                    print(f"✅ Successfully returned via back button")
                    return True
            except Exception as e:
                print(f"   ⚠️ Back button failed: {e}")

            # Step 4: If we have original URL, navigate directly
            if original_url:
                print(f"   🌐 Navigating to original URL: {original_url}")
                try:
                    self.wd.get(original_url)
                    time.sleep(5)

                    if self._verify_ready_for_next_image():
                        print(f"✅ Successfully returned via direct navigation")
                        return True
                except Exception as e:
                    print(f"   ⚠️ Direct navigation failed: {e}")

            # Step 5: Try page refresh as last resort
            print("   🔄 Trying page refresh...")
            try:
                self.wd.refresh()
                time.sleep(5)

                if self._verify_ready_for_next_image():
                    print(f"✅ Successfully recovered via refresh")
                    return True
            except Exception as e:
                print(f"   ⚠️ Refresh failed: {e}")

            print(f"❌ All navigation attempts failed for image {image_index + 1}")
            return False

        except Exception as e:
            print(f"❌ Error in context-aware navigation for image {image_index + 1}: {e}")
            return False

    def _is_gallery_page(self, url):
        """Check if the current URL is a gallery page"""
        try:
            gallery_indicators = [
                'catalog',
                'media',
                'ebird.org',
                'gallery'
            ]

            url_lower = url.lower()
            return any(indicator in url_lower for indicator in gallery_indicators)
        except:
            return False

    def _store_original_gallery_url(self):
        """Store the original gallery URL for navigation purposes"""
        try:
            current_url = self.wd.current_url
            if self._is_gallery_page(current_url):
                self._original_gallery_url = current_url
                print(f"📌 Stored original gallery URL: {current_url}")
        except Exception as e:
            print(f"⚠️ Could not store original URL: {e}")

    def _verify_ready_for_next_image(self):
        """Verify that the page is ready for processing the next image"""
        try:
            # Check that we have clickable images available
            clickable_images = self._get_clickable_images_from_page()

            if len(clickable_images) > 0:
                print(f"   ✅ Found {len(clickable_images)} clickable images, ready for next")
                return True
            else:
                print(f"   ⚠️ No clickable images found, page may not be ready")
                return False

        except Exception as e:
            print(f"   ❌ Error verifying readiness for next image: {e}")
            return False

    def _recover_page_state(self):
        """Recover page state when navigation or page readiness fails"""
        print("🔧 Attempting to recover page state...")

        try:
            # Strategy 1: Refresh the page
            print("   🔄 Refreshing page...")
            self.wd.refresh()
            time.sleep(5)

            # Wait for page to load
            if self._wait_for_gallery_ready(timeout=15):
                print("   ✅ Page recovered successfully via refresh")
                return True

            # Strategy 2: Navigate back to original URL
            print("   🌐 Attempting to navigate back to original URL...")
            current_url = self.wd.current_url
            if 'catalog' in current_url and 'ebird.org' in current_url:
                self.wd.get(current_url)
                time.sleep(5)

                if self._wait_for_gallery_ready(timeout=15):
                    print("   ✅ Page recovered successfully via navigation")
                    return True

            # Strategy 3: Try browser back button
            print("   ⬅️ Trying browser back button...")
            self.wd.back()
            time.sleep(3)

            if self._wait_for_gallery_ready(timeout=10):
                print("   ✅ Page recovered successfully via back button")
                return True

            print("   ❌ All recovery strategies failed")
            return False

        except Exception as e:
            print(f"   ❌ Error during page recovery: {e}")
            return False

    def _find_full_resolution_image(self):
        """Enhanced detection untuk gambar resolusi penuh - mencari URL terbaik"""
        try:
            print(f"🔍 Searching for FULL RESOLUTION image URL...")

            # Wait for image to fully load
            time.sleep(3)

            # Ultra high-priority selectors untuk URL resolusi maksimal
            ultra_high_res_selectors = [
                # Modal dan viewer images (prioritas tertinggi)
                ".modal img[src*='macaulaylibrary.org']:not([src*='thumbnail']):not([src*='small'])",
                ".lightbox img[src*='macaulaylibrary.org']:not([src*='thumbnail']):not([src*='small'])",
                ".fullscreen img[src*='macaulaylibrary.org']:not([src*='thumbnail']):not([src*='small'])",

                # eBird specific viewers
                ".MediaDisplay img[src*='macaulaylibrary.org']",
                ".MediaViewer img[src*='macaulaylibrary.org']",
                ".FullscreenImage img[src*='macaulaylibrary.org']",
                "[data-testid='media-display'] img[src*='macaulaylibrary.org']",

                # Images dengan indikator resolusi tinggi
                "img[src*='macaulaylibrary.org'][src*='original']",
                "img[src*='macaulaylibrary.org'][src*='full']",
                "img[src*='macaulaylibrary.org'][src*='xlarge']",
                "img[src*='macaulaylibrary.org'][src*='2400']",
                "img[src*='macaulaylibrary.org'][src*='1920']",

                # Cornell CDN high-res
                "img[src*='cdn.download.ams.birds.cornell.edu']:not([src*='thumbnail'])",

                # General high-quality indicators
                "img[style*='max-width'][src*='macaulaylibrary.org']",
                "img[alt*='photo'][src*='macaulaylibrary.org']",

                # Fallback ke semua eBird images
                "img[src*='macaulaylibrary.org']"
            ]

            best_url = None
            best_score = 0

            for selector in ultra_high_res_selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)

                    for img in images:
                        if img.is_displayed():
                            img_url = img.get_attribute('src')

                            if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                                # Calculate URL quality score
                                score = self._calculate_url_quality_score(img_url, img)

                                if score > best_score:
                                    best_url = img_url
                                    best_score = score
                                    print(f"🎯 Found better URL (score: {score}): {img_url[:80]}...")

                except Exception as e:
                    continue

            if best_url:
                # Convert to absolute highest resolution
                ultra_high_res_url = self._convert_to_ultra_high_res(best_url)
                print(f"✅ BEST RESOLUTION URL found (score: {best_score})")
                return ultra_high_res_url
            else:
                print(f"⚠️ No high-resolution URL found")
                return None

        except Exception as e:
            print(f"❌ Error finding full resolution image: {e}")
            return None

    def _calculate_url_quality_score(self, url, element):
        """Calculate quality score for an image URL"""
        try:
            score = 0
            url_lower = url.lower()

            # URL quality indicators (higher score = better quality)
            if 'original' in url_lower:
                score += 1000
            elif 'full' in url_lower:
                score += 800
            elif 'xlarge' in url_lower:
                score += 600
            elif 'large' in url_lower:
                score += 400
            elif '2400' in url or '1920' in url:
                score += 500

            # Penalties for low quality indicators
            if 'thumbnail' in url_lower or 'thumb' in url_lower:
                score -= 800
            elif 'small' in url_lower:
                score -= 400
            elif 'medium' in url_lower:
                score -= 200

            # Element size bonus
            try:
                size = element.size
                area = size['width'] * size['height']
                score += area / 1000  # Normalize area to score
            except:
                pass

            # Context bonus (modal/viewer context is better)
            try:
                parent_classes = element.find_element(By.XPATH, './..').get_attribute('class') or ''
                if any(cls in parent_classes.lower() for cls in ['modal', 'viewer', 'fullscreen', 'lightbox']):
                    score += 300
            except:
                pass

            return score

        except:
            return 0

    def _convert_to_ultra_high_res(self, img_url):
        """Convert URL to absolute maximum resolution"""
        if not img_url:
            return None

        try:
            print(f"🚀 Converting to ULTRA HIGH RESOLUTION...")
            original_url = img_url

            if 'macaulaylibrary.org' in img_url:
                # Remove all size parameters
                base_url = img_url.split('?')[0]

                # Try the absolute best resolution parameters
                ultra_params = [
                    '?size=original&quality=100',
                    '?width=5000&height=4000&quality=100', 
                    '?size=full&quality=100',
                    '?size=xlarge&quality=100',
                    '?width=4000&height=3000&quality=100', 
                    '?size=4K&quality=100',   
                    '?w=2400&h=1600&q=100',
                    '?size=2400&quality=100',
                    '?original=true',
                    ''  # Sometimes no parameters gives original
                ]

                # Test each parameter and pick the best
                for param in ultra_params:
                    test_url = base_url + param
                    if self._verify_high_quality_url(test_url):
                        print(f"✅ Ultra high-res URL verified: {param}")
                        return test_url

                # If no verification possible, use the best parameter
                return base_url + '?size=original&quality=100'

            elif 'cdn.download.ams.birds.cornell.edu' in img_url:
                # Cornell CDN optimization
                img_url = img_url.replace('/thumbnails/', '/catalog/')
                img_url = img_url.replace('_thumbnail', '')
                img_url = img_url.replace('_small', '')
                img_url = img_url.replace('_medium', '')
                img_url = img_url.replace('_large', '')

                # Add quality parameters
                if '?' not in img_url:
                    img_url += '?quality=100&size=original'

                return img_url

            return img_url

        except Exception as e:
            print(f"❌ Error converting to ultra high-res: {e}")
            return original_url

    def _verify_high_quality_url(self, url):
        """Verify if URL returns high quality image"""
        try:
            import requests
            response = requests.head(url, timeout=3)
            if response.status_code == 200:
                content_length = response.headers.get('content-length')
                if content_length:
                    size_kb = int(content_length) / 1024
                    # High quality images should be at least 100KB
                    return size_kb > 100
        except:
            pass
        return False

    def _take_screenshot(self, index, output_dir=None, crop_to_bird=True):
        """Ultra high-quality screenshot functionality - mimics manual screenshot behavior"""
        try:
            print(f"📸 Taking ULTRA HIGH-QUALITY screenshot for image {index + 1}...")

            # Set output directory
            if output_dir is None:
                output_dir = os.getcwd()

            # Wait for full loading and animations
            time.sleep(3)

            # Set browser to maximum quality mode
            self._optimize_browser_for_screenshots()

            # Hide all UI overlays and distractions
            self._hide_ui_overlays()

            # Try to maximize the image display first
            self._maximize_image_display()

            # Find the absolute best image element
            screenshot_element = self._find_ultra_high_res_target()

            if screenshot_element:
                # Get element dimensions and optimize
                element_size = screenshot_element.size
                print(f"🎯 Target element size: {element_size['width']}x{element_size['height']} pixels")

                # Scroll and center perfectly
                self.wd.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'center'
                    });

                    // Remove any transforms or filters that might reduce quality
                    arguments[0].style.transform = 'none';
                    arguments[0].style.filter = 'none';
                    arguments[0].style.opacity = '1';

                    // Ensure maximum quality display
                    arguments[0].style.imageRendering = 'high-quality';
                    arguments[0].style.imageRendering = '-webkit-optimize-contrast';
                """, screenshot_element)

                time.sleep(2)  # Wait for rendering

                # Take ultra high-quality screenshot
                screenshot_filename = f"ebird_ultra_hq_{index + 1:03d}.png"
                screenshot_path = os.path.join(output_dir, screenshot_filename)

                # Try multiple screenshot methods for best quality
                success = self._take_ultra_quality_screenshot(screenshot_element, screenshot_path, crop_to_bird)

                if success:
                    file_size = os.path.getsize(screenshot_path)
                    print(f"✅ ULTRA HIGH-QUALITY screenshot saved: {screenshot_filename}")
                    print(f"📊 File size: {file_size/1024:.1f} KB")
                    return screenshot_path
                else:
                    print("🔄 Ultra quality failed, trying enhanced fallback...")
                    return self._take_enhanced_fallback_screenshot(index, output_dir)

            else:
                print("🔄 No ultra-high-res target found, trying enhanced fallback...")
                return self._take_enhanced_fallback_screenshot(index, output_dir)

        except Exception as e:
            print(f"❌ Error in ultra high-quality screenshot: {e}")
            return self._take_enhanced_fallback_screenshot(index, output_dir)

    def _optimize_browser_for_screenshots(self):
        """Optimize browser settings for maximum screenshot quality"""
        try:
            # Set maximum zoom for better quality
            self.wd.execute_script("document.body.style.zoom = '1.0';")

            # Disable any image compression or optimization
            self.wd.execute_script("""
                // Disable image optimization
                var style = document.createElement('style');
                style.textContent = `
                    img {
                        image-rendering: -webkit-optimize-contrast !important;
                        image-rendering: high-quality !important;
                        image-rendering: crisp-edges !important;
                        -ms-interpolation-mode: bicubic !important;
                    }
                `;
                document.head.appendChild(style);
            """)

            # Wait for styles to apply
            time.sleep(1)

        except Exception as e:
            print(f"⚠️ Warning: Could not optimize browser for screenshots: {e}")

    def _maximize_image_display(self):
        """Try to maximize the image display like manual viewing"""
        try:
            # Look for fullscreen or maximize buttons
            maximize_selectors = [
                'button[aria-label*="fullscreen"]',
                'button[aria-label*="maximize"]',
                'button[title*="fullscreen"]',
                'button[title*="maximize"]',
                '.fullscreen-button',
                '.maximize-button',
                '[data-testid*="fullscreen"]',
                '[data-testid*="maximize"]'
            ]

            for selector in maximize_selectors:
                try:
                    button = self.wd.find_element(By.CSS_SELECTOR, selector)
                    if button.is_displayed() and button.is_enabled():
                        print(f"🔍 Found maximize button, clicking...")
                        button.click()
                        time.sleep(2)
                        return True
                except:
                    continue

            # Try keyboard shortcut for fullscreen
            try:
                from selenium.webdriver.common.keys import Keys
                body = self.wd.find_element(By.TAG_NAME, 'body')
                body.send_keys(Keys.F11)  # Try F11 for fullscreen
                time.sleep(1)
            except:
                pass

        except Exception as e:
            print(f"⚠️ Could not maximize image display: {e}")

        return False

    def _find_ultra_high_res_target(self):
        """Find the absolute best, highest resolution image element"""
        try:
            # Ultra high priority selectors for maximum quality
            ultra_selectors = [
                # Full-size modal images (highest priority)
                '.modal img[src*="macaulaylibrary.org"]:not([src*="thumbnail"]):not([src*="small"])',
                '.lightbox img[src*="macaulaylibrary.org"]:not([src*="thumbnail"]):not([src*="small"])',
                '.fullscreen img[src*="macaulaylibrary.org"]:not([src*="thumbnail"]):not([src*="small"])',

                # eBird specific high-res viewers
                '.MediaDisplay img[src*="macaulaylibrary.org"]',
                '.MediaViewer img[src*="macaulaylibrary.org"]',
                '.FullscreenImage img[src*="macaulaylibrary.org"]',
                '[data-testid="media-display"] img[src*="macaulaylibrary.org"]',

                # Large display images with size indicators
                'img[src*="macaulaylibrary.org"][src*="original"]',
                'img[src*="macaulaylibrary.org"][src*="full"]',
                'img[src*="macaulaylibrary.org"][src*="xlarge"]',
                'img[src*="macaulaylibrary.org"][src*="2400"]',
                'img[src*="macaulaylibrary.org"][src*="1920"]',

                # Cornell CDN high-res images
                'img[src*="cdn.download.ams.birds.cornell.edu"]:not([src*="thumbnail"])',

                # Any large eBird image
                'img[src*="macaulaylibrary.org"]'
            ]

            best_element = None
            best_score = 0

            for selector in ultra_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Calculate quality score
                            score = self._calculate_image_quality_score(element)

                            if score > best_score:
                                best_element = element
                                best_score = score

                except Exception as e:
                    continue

            if best_element:
                print(f"🎯 Found ULTRA HIGH-RES target with quality score: {best_score}")
                return best_element
            else:
                print("⚠️ No ultra high-res target found")
                return None

        except Exception as e:
            print(f"❌ Error finding ultra high-res target: {e}")
            return None

    def _calculate_image_quality_score(self, element):
        """Calculate quality score for an image element"""
        try:
            score = 0

            # Size score (larger is better)
            size = element.size
            area = size['width'] * size['height']
            score += area / 1000  # Normalize

            # URL quality indicators
            src = element.get_attribute('src') or ''
            if 'original' in src.lower():
                score += 1000
            elif 'full' in src.lower():
                score += 800
            elif 'xlarge' in src.lower():
                score += 600
            elif 'large' in src.lower():
                score += 400
            elif '2400' in src or '1920' in src:
                score += 500

            # Penalty for thumbnails
            if 'thumbnail' in src.lower() or 'thumb' in src.lower():
                score -= 500
            if 'small' in src.lower():
                score -= 300

            # Bonus for modal/viewer context
            parent_classes = element.find_element(By.XPATH, './..').get_attribute('class') or ''
            if any(cls in parent_classes.lower() for cls in ['modal', 'viewer', 'fullscreen', 'lightbox']):
                score += 200

            return score

        except:
            return 0

    def _take_ultra_quality_screenshot(self, element, screenshot_path, crop_to_bird=True):
        """Take the highest quality screenshot with improved reliability"""
        try:
            print(f"   📸 Attempting ultra-quality screenshot...")

            # Method 1: Element screenshot with validation
            try:
                element.screenshot(screenshot_path)
                if self._validate_screenshot_quality(screenshot_path):
                    print(f"   ✅ Element screenshot successful")
                    if crop_to_bird:
                        return self._safe_crop_screenshot(screenshot_path)
                    return True
                else:
                    print(f"   ⚠️ Element screenshot quality insufficient")
            except Exception as e:
                print(f"   ⚠️ Element screenshot failed: {e}")

            # Method 2: Full page screenshot with element focus
            try:
                # Scroll element to center of viewport for better capture
                self.wd.execute_script("""
                    arguments[0].scrollIntoView({
                        behavior: 'instant',
                        block: 'center',
                        inline: 'center'
                    });
                """, element)
                time.sleep(1)

                self.wd.save_screenshot(screenshot_path)
                if self._validate_screenshot_quality(screenshot_path):
                    print(f"   ✅ Full page screenshot successful")
                    if crop_to_bird:
                        return self._safe_crop_screenshot(screenshot_path)
                    return True
                else:
                    print(f"   ⚠️ Full page screenshot quality insufficient")
            except Exception as e:
                print(f"   ⚠️ Full page screenshot failed: {e}")

            return False

        except Exception as e:
            print(f"❌ Error in ultra quality screenshot: {e}")
            return False

    def _validate_screenshot_quality(self, screenshot_path):
        """Validate screenshot quality and detect common issues"""
        try:
            if not os.path.exists(screenshot_path):
                return False

            file_size = os.path.getsize(screenshot_path)
            if file_size < 10000:  # Less than 10KB is likely too small
                print(f"   ❌ Screenshot too small: {file_size} bytes")
                return False

            # Check image dimensions and content
            with Image.open(screenshot_path) as img:
                width, height = img.size
                if width < 100 or height < 100:
                    print(f"   ❌ Screenshot dimensions too small: {width}x{height}")
                    return False

                # Check if image is mostly black/white (common error)
                img_array = np.array(img.convert('L'))  # Convert to grayscale
                mean_brightness = np.mean(img_array)
                if mean_brightness < 10 or mean_brightness > 245:
                    print(f"   ❌ Screenshot appears corrupted (brightness: {mean_brightness})")
                    return False

                print(f"   ✅ Screenshot quality validated: {width}x{height}, {file_size/1024:.1f}KB")
                return True

        except Exception as e:
            print(f"   ❌ Error validating screenshot: {e}")
            return False

    def _safe_crop_screenshot(self, screenshot_path):
        """Safely crop screenshot with fallback to original if cropping fails"""
        try:
            print(f"   ✂️ Attempting safe crop...")

            # Create backup of original
            backup_path = screenshot_path + ".backup"
            import shutil
            shutil.copy2(screenshot_path, backup_path)

            # Attempt cropping
            cropped_path = self._crop_screenshot_to_bird_image(screenshot_path)

            # Validate cropped result
            if cropped_path and self._validate_screenshot_quality(cropped_path):
                print(f"   ✅ Cropping successful")
                # Remove backup
                if os.path.exists(backup_path):
                    os.remove(backup_path)
                return True
            else:
                print(f"   ⚠️ Cropping failed or produced poor quality, restoring original")
                # Restore original from backup
                if os.path.exists(backup_path):
                    shutil.move(backup_path, screenshot_path)
                return True

        except Exception as e:
            print(f"   ❌ Error in safe crop: {e}")
            # Try to restore backup if it exists
            backup_path = screenshot_path + ".backup"
            if os.path.exists(backup_path):
                try:
                    import shutil
                    shutil.move(backup_path, screenshot_path)
                    print(f"   ✅ Original screenshot restored")
                except:
                    pass
            return True  # Return True even if crop failed, original should still be valid

    def _hide_ui_overlays(self):
        """Hide UI overlays and elements that might interfere with screenshots"""
        try:
            # Common overlay selectors to hide
            overlay_selectors = [
                '.overlay',
                '.modal-overlay',
                '.popup-overlay',
                '.tooltip',
                '.dropdown-menu',
                'nav',
                'header',
                '.navigation',
                '.toolbar',
                '.controls',
                '.ui-controls',
                '[class*="overlay"]',
                '[class*="popup"]',
                '[class*="tooltip"]'
            ]

            hidden_elements = []
            for selector in overlay_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Store original display style
                            original_style = element.get_attribute('style')
                            # Hide element
                            self.wd.execute_script("arguments[0].style.display = 'none';", element)
                            hidden_elements.append((element, original_style))
                except:
                    continue

            # Store hidden elements for potential restoration
            self._hidden_elements = hidden_elements

        except Exception as e:
            print(f"Warning: Could not hide UI overlays: {e}")

    def _find_best_screenshot_target(self):
        """Find the best image element for screenshot"""
        try:
            # Priority-ordered selectors for finding the main image
            image_selectors = [
                # Modal/viewer images (highest priority)
                '.modal img[src*="macaulaylibrary.org"]',
                '.viewer img[src*="macaulaylibrary.org"]',
                '.lightbox img[src*="macaulaylibrary.org"]',
                '.fullscreen img[src*="macaulaylibrary.org"]',
                '.MediaDisplay img',
                '.MediaViewer img',
                '.FullscreenImage img',
                '[data-testid="media-display"] img',

                # Large display images
                'img[class*="large"][src*="macaulaylibrary.org"]',
                'img[class*="full"][src*="macaulaylibrary.org"]',
                'img[class*="detail"][src*="macaulaylibrary.org"]',

                # General eBird images
                'img[src*="macaulaylibrary.org"]',
                'img[src*="cdn.download.ams.birds.cornell.edu"]',

                # Fallback to any large visible image
                'img[width][height]'
            ]

            best_element = None
            best_size = 0

            for selector in image_selectors:
                try:
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements:
                        if element.is_displayed():
                            # Calculate element size
                            size = element.size
                            element_area = size['width'] * size['height']

                            # Prefer larger images
                            if element_area > best_size:
                                best_element = element
                                best_size = element_area

                except Exception as e:
                    continue

            if best_element:
                print(f"Found best screenshot target: {best_size} pixels area")
                return best_element
            else:
                print("No suitable screenshot target found")
                return None

        except Exception as e:
            print(f"Error finding screenshot target: {e}")
            return None

    def _take_enhanced_fallback_screenshot(self, index, output_dir):
        """Enhanced fallback screenshot with multiple strategies"""
        try:
            print(f"🔄 Taking enhanced fallback screenshot for image {index + 1}...")

            # Strategy 1: Try to find any large image on the page
            large_images = self._find_large_images_on_page()
            if large_images:
                for img in large_images[:3]:  # Try top 3 largest images
                    try:
                        screenshot_filename = f"ebird_fallback_{index + 1:03d}.png"
                        screenshot_path = os.path.join(output_dir, screenshot_filename)

                        # Scroll to image and take screenshot
                        self.wd.execute_script("arguments[0].scrollIntoView(true);", img)
                        time.sleep(1)

                        img.screenshot(screenshot_path)

                        if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 5000:
                            print(f"✅ Enhanced fallback screenshot saved: {screenshot_filename}")
                            return screenshot_path
                    except:
                        continue

            # Strategy 2: Full page screenshot with optimization
            screenshot_filename = f"ebird_fullpage_{index + 1:03d}.png"
            screenshot_path = os.path.join(output_dir, screenshot_filename)

            # Optimize page for screenshot
            self.wd.execute_script("""
                // Hide navigation and UI elements
                var elements = document.querySelectorAll('nav, header, .navigation, .toolbar');
                elements.forEach(el => el.style.display = 'none');

                // Maximize content area
                document.body.style.margin = '0';
                document.body.style.padding = '0';
            """)

            time.sleep(1)
            self.wd.save_screenshot(screenshot_path)

            if os.path.exists(screenshot_path) and os.path.getsize(screenshot_path) > 1000:
                print(f"✅ Full page fallback screenshot saved: {screenshot_filename}")
                return screenshot_path
            else:
                print(f"❌ Failed to create any fallback screenshot")
                return None

        except Exception as e:
            print(f"❌ Error taking enhanced fallback screenshot: {e}")
            return None

    def _find_large_images_on_page(self):
        """Find all large images on the current page"""
        try:
            all_images = self.wd.find_elements(By.TAG_NAME, 'img')
            large_images = []

            for img in all_images:
                try:
                    if img.is_displayed():
                        size = img.size
                        area = size['width'] * size['height']

                        # Consider images larger than 100x100 pixels
                        if area > 10000:  # 100x100 = 10,000 pixels
                            large_images.append((img, area))
                except:
                    continue

            # Sort by area (largest first)
            large_images.sort(key=lambda x: x[1], reverse=True)

            # Return just the image elements
            return [img for img, area in large_images]

        except Exception as e:
            print(f"❌ Error finding large images: {e}")
            return []

    def _crop_screenshot_to_bird_image(self, screenshot_path):
        """Simplified intelligent cropping focused on bird images with high contrast"""
        try:
            print(f"✂️ Starting simplified bird image cropping...")

            # Load the screenshot
            original_img = Image.open(screenshot_path)
            print(f"📏 Original image size: {original_img.size}")

            # Apply simplified cropping algorithm
            cropped_img = self._simple_bird_crop(original_img)

            if cropped_img and cropped_img != original_img:
                # Save the cropped result
                cropped_img.save(screenshot_path)
                print(f"✅ Bird image cropped successfully!")
                print(f"📏 Final size: {cropped_img.size}")
                return screenshot_path
            else:
                print(f"⚠️ Cropping not applied, keeping original")
                return screenshot_path

        except Exception as e:
            print(f"❌ Error in bird cropping: {e}")
            return screenshot_path

    def _simple_bird_crop(self, image):
        """Enhanced bird cropping algorithm optimized for high-contrast bird images"""
        try:
            print(f"   🔍 Starting enhanced bird crop analysis...")

            # Convert PIL image to OpenCV format
            img_array = np.array(image)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # Get image dimensions
            img_height, img_width = img_cv.shape[:2]
            print(f"   📏 Image dimensions: {img_width}x{img_height}")

            # Calculate the height for a 16:9 aspect ratio based on the image width
            target_height = int(img_width * 9 / 16)
            print(f"   📐 Target 16:9 height: {target_height}")

            # Convert to grayscale
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # For high-contrast images, use less aggressive blur
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)

            # Use more sensitive edge detection for high-contrast images
            # Lower thresholds to catch more edges in high-contrast scenarios
            edges = cv2.Canny(blurred, 30, 100)

            # Apply morphological operations to connect nearby edges
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)

            # Find contours
            contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                print("   ⚠️ No contours found, trying alternative approach...")
                # Fallback: use simple center crop
                return self._fallback_center_crop(image, target_height)

            print(f"   🔍 Found {len(contours)} contours")

            # Filter contours by area (remove very small ones)
            min_area = (img_width * img_height) * 0.01  # At least 1% of image area
            valid_contours = [c for c in contours if cv2.contourArea(c) > min_area]

            if not valid_contours:
                print("   ⚠️ No significant contours found, using fallback...")
                return self._fallback_center_crop(image, target_height)

            print(f"   ✅ Found {len(valid_contours)} significant contours")

            # Find the largest contour (assuming it's the main object/bird)
            largest_contour = max(valid_contours, key=cv2.contourArea)
            contour_area = cv2.contourArea(largest_contour)
            print(f"   📊 Largest contour area: {contour_area:.0f} pixels")

            # Get the bounding box around the largest contour
            x, y, w, h = cv2.boundingRect(largest_contour)
            print(f"   📦 Bounding box: x={x}, y={y}, w={w}, h={h}")

            # Calculate the center of the largest contour
            center_x = x + w // 2
            center_y = y + h // 2
            print(f"   🎯 Object center: ({center_x}, {center_y})")

            # Calculate the y-coordinate for the crop to center the important area within the lower third of the 16:9 crop
            crop_y = max(0, center_y - (2 * target_height) // 3)
            crop_y = min(crop_y, img_height - target_height)  # Ensure crop is within image bounds
            print(f"   ✂️ Crop Y position: {crop_y}")

            # Only crop if the target height is smaller than the original height
            if target_height < img_height:
                # Crop the image to the specified width and target height
                cropped_cv = img_cv[crop_y:crop_y + target_height, 0:img_width]

                # Convert back to PIL Image
                cropped_rgb = cv2.cvtColor(cropped_cv, cv2.COLOR_BGR2RGB)
                cropped_image = Image.fromarray(cropped_rgb)

                print(f"   ✅ Successfully cropped from {img_width}x{img_height} to {cropped_image.width}x{cropped_image.height}")
                return cropped_image
            else:
                print(f"   ⚠️ Target height ({target_height}) >= original height ({img_height}), keeping original")
                return image

        except Exception as e:
            print(f"   ❌ Error in enhanced bird crop: {e}")
            import traceback
            traceback.print_exc()
            return image

    def _fallback_center_crop(self, image, target_height):
        """Fallback center crop when edge detection fails"""
        try:
            print(f"   🔄 Applying fallback center crop...")
            img_width, img_height = image.size

            if target_height < img_height:
                # Center crop
                crop_y = (img_height - target_height) // 2
                cropped_image = image.crop((0, crop_y, img_width, crop_y + target_height))
                print(f"   ✅ Fallback crop: {img_width}x{img_height} to {cropped_image.width}x{cropped_image.height}")
                return cropped_image
            else:
                print(f"   ⚠️ No cropping needed for fallback")
                return image
        except Exception as e:
            print(f"   ❌ Error in fallback crop: {e}")
            return image

    def _crop_downloaded_image(self, filepath):
        """Apply cropping to a downloaded image file"""
        try:
            print(f"   🔍 Loading downloaded image for cropping: {os.path.basename(filepath)}")

            # Load the downloaded image
            original_img = Image.open(filepath)
            print(f"   📏 Original downloaded image size: {original_img.size}")

            # Apply simplified cropping algorithm
            cropped_img = self._simple_bird_crop(original_img)

            if cropped_img and cropped_img.size != original_img.size:
                # Save the cropped result back to the same file
                cropped_img.save(filepath, quality=95, optimize=True)
                print(f"   ✅ Downloaded image cropped successfully!")
                print(f"   📏 Final size: {cropped_img.size}")
                return filepath
            else:
                print(f"   ⚠️ No cropping applied to downloaded image")
                return filepath

        except Exception as e:
            print(f"   ❌ Error cropping downloaded image: {e}")
            return filepath

    def _detect_and_crop_bird_area(self, img):
        """Advanced intelligent bird image detection and cropping with multiple strategies"""
        try:
            # Convert PIL image to OpenCV format
            img_array = np.array(img)
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            print(f"🔍 Analyzing image dimensions: {img.width}x{img.height}")

            # Strategy 1: Boundary detection and precise cropping
            cropped = self._boundary_detection_crop(img_cv, img)
            if cropped:
                return cropped

            # Strategy 2: Advanced edge detection with morphological operations
            cropped = self._advanced_edge_detection_crop(img_cv, img)
            if cropped:
                return cropped

            # Strategy 3: Advanced color tracking and segmentation for bird detection
            cropped = self._intelligent_color_tracking_crop(img_cv, img)
            if cropped:
                return cropped

            # Strategy 3b: Fallback to basic color segmentation
            cropped = self._color_segmentation_crop(img_cv, img)
            if cropped:
                return cropped

            # Strategy 4: Template matching for eBird UI elements
            cropped = self._ebird_ui_aware_crop(img_cv, img)
            if cropped:
                return cropped

            # Strategy 5: Intelligent content-aware cropping
            cropped = self._content_aware_crop(img_cv, img)
            if cropped:
                return cropped

            # Strategy 6: Fallback to enhanced center crop
            cropped = self._enhanced_center_crop(img)
            if cropped:
                return cropped

            return None

        except Exception as e:
            print(f"❌ Error in bird area detection: {e}")
            return None

    def _detect_and_crop_bird_area_with_debug(self, img, debug_dir=None, session_id=None):
        """Simplified version that uses the simple bird crop method"""
        try:
            print(f"🔍 Using simplified bird cropping method...")

            # Use our simplified cropping method
            result = self._simple_bird_crop(img)

            if result and result != img:
                print(f"✅ Simple crop successful: {result.width}x{result.height}")
                return result, "simple_bird_crop"
            else:
                print(f"⚠️ Simple crop returned original image")
                return None, "none"

        except Exception as e:
            print(f"❌ Error in simplified bird cropping: {e}")
            return None, "error"

    def _advanced_edge_detection_crop(self, img_cv, img_pil):
        """Advanced edge detection with morphological operations for precise bird boundary detection"""
        try:
            print("🔍 Applying advanced edge detection...")

            # Convert to grayscale
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # Apply bilateral filter to reduce noise while preserving edges
            filtered = cv2.bilateralFilter(gray, 9, 75, 75)

            # Apply adaptive histogram equalization for better contrast
            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            enhanced = clahe.apply(filtered)

            # Multi-scale edge detection
            edges1 = cv2.Canny(enhanced, 30, 100)
            edges2 = cv2.Canny(enhanced, 50, 150)
            edges3 = cv2.Canny(enhanced, 100, 200)

            # Combine edges from different scales
            combined_edges = cv2.bitwise_or(edges1, cv2.bitwise_or(edges2, edges3))

            # Morphological operations to close gaps and remove noise
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            closed = cv2.morphologyEx(combined_edges, cv2.MORPH_CLOSE, kernel)

            # Fill holes
            kernel_fill = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            filled = cv2.morphologyEx(closed, cv2.MORPH_DILATE, kernel_fill)

            # Find contours
            contours, _ = cv2.findContours(filled, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                print("⚠️ No contours found in edge detection")
                return None

            # Filter contours by area and aspect ratio
            valid_contours = []
            min_area = (img_pil.width * img_pil.height) * 0.05  # At least 5% of image
            max_area = (img_pil.width * img_pil.height) * 0.8   # At most 80% of image

            for contour in contours:
                area = cv2.contourArea(contour)
                if min_area < area < max_area:
                    # Check aspect ratio
                    x, y, w, h = cv2.boundingRect(contour)
                    aspect_ratio = w / h
                    if 0.3 < aspect_ratio < 3.0:  # Reasonable aspect ratios
                        valid_contours.append((contour, area))

            if not valid_contours:
                print("⚠️ No valid contours found")
                return None

            # Sort by area and take the largest valid contour
            valid_contours.sort(key=lambda x: x[1], reverse=True)
            best_contour = valid_contours[0][0]

            # Get bounding rectangle with smart padding
            x, y, w, h = cv2.boundingRect(best_contour)

            # Dynamic padding based on image size
            padding_x = max(10, int(w * 0.05))
            padding_y = max(10, int(h * 0.05))

            # Apply padding with bounds checking
            x = max(0, x - padding_x)
            y = max(0, y - padding_y)
            w = min(img_pil.width - x, w + 2 * padding_x)
            h = min(img_pil.height - y, h + 2 * padding_y)

            # Ensure minimum crop size
            if w < 200 or h < 200:
                print("⚠️ Detected area too small")
                return None

            # Crop the image
            cropped = img_pil.crop((x, y, x + w, y + h))

            print(f"✅ Advanced edge detection crop: {w}x{h} at ({x}, {y})")
            return cropped

        except Exception as e:
            print(f"❌ Error in advanced edge detection: {e}")
            return None

    def _intelligent_color_tracking_crop(self, img_cv, img_pil):
        """Advanced intelligent color tracking for precise bird detection and cropping"""
        try:
            print("🎨 Applying intelligent color tracking analysis...")

            # Step 1: Extract dominant colors using K-means clustering
            dominant_colors = self._extract_dominant_colors(img_pil)
            if not dominant_colors:
                print("⚠️ Failed to extract dominant colors")
                return None

            # Step 2: Identify bird type based on color profile
            bird_type = self._identify_bird_type_from_colors(dominant_colors)
            print(f"🐦 Detected bird type: {bird_type}")

            # Step 3: Create intelligent color mask
            color_mask = self._create_intelligent_color_mask(img_cv, bird_type, dominant_colors)
            if color_mask is None:
                print("⚠️ Failed to create intelligent color mask")
                return None

            # Step 4: Find optimal contour and crop
            return self._crop_using_intelligent_mask(img_pil, color_mask, bird_type, dominant_colors)

        except Exception as e:
            print(f"❌ Error in intelligent color tracking: {e}")
            return None

    def _extract_dominant_colors(self, img_pil, n_colors=5):
        """Extract dominant colors using K-means clustering"""
        try:
            # Import sklearn here to avoid dependency issues
            from sklearn.cluster import KMeans
            from collections import Counter

            # Convert to numpy array
            img_array = np.array(img_pil)

            # Reshape to list of pixels
            pixels = img_array.reshape(-1, 3)

            # Filter out very dark and very bright pixels (likely background/UI)
            mask = np.all(pixels > 15, axis=1) & np.all(pixels < 245, axis=1)
            filtered_pixels = pixels[mask]

            if len(filtered_pixels) < 100:
                return []

            # Apply K-means clustering
            n_clusters = min(n_colors, len(filtered_pixels) // 100)
            kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
            kmeans.fit(filtered_pixels)

            # Get dominant colors and their frequencies
            colors = kmeans.cluster_centers_.astype(int)
            labels = kmeans.labels_
            color_counts = Counter(labels)
            total_pixels = len(labels)

            dominant_colors = []
            for i, color in enumerate(colors):
                frequency = color_counts[i] / total_pixels
                hsv = self._rgb_to_hsv_opencv(color)
                dominant_colors.append({
                    'rgb': tuple(color),
                    'hsv': hsv,
                    'frequency': frequency
                })

            # Sort by frequency
            dominant_colors.sort(key=lambda x: x['frequency'], reverse=True)

            print(f"🎨 Extracted {len(dominant_colors)} dominant colors")
            for i, color_info in enumerate(dominant_colors[:3]):
                print(f"   Color {i+1}: RGB{color_info['rgb']} ({color_info['frequency']:.1%})")

            return dominant_colors

        except ImportError:
            print("⚠️ sklearn not available, falling back to basic color analysis")
            return self._extract_colors_basic(img_pil)
        except Exception as e:
            print(f"❌ Error extracting dominant colors: {e}")
            return []

    def _extract_colors_basic(self, img_pil):
        """Basic color extraction without sklearn dependency"""
        try:
            # Simple histogram-based approach
            img_array = np.array(img_pil)

            # Sample pixels from the image
            h, w = img_array.shape[:2]
            sample_step = max(1, min(h, w) // 50)  # Sample every N pixels

            sampled_pixels = []
            for y in range(0, h, sample_step):
                for x in range(0, w, sample_step):
                    pixel = img_array[y, x]
                    # Filter out very dark and bright pixels
                    if 15 < np.mean(pixel) < 245:
                        sampled_pixels.append(pixel)

            if not sampled_pixels:
                return []

            # Group similar colors
            color_groups = {}
            tolerance = 30

            for pixel in sampled_pixels:
                found_group = False
                for group_color in color_groups:
                    if np.linalg.norm(np.array(pixel) - np.array(group_color)) < tolerance:
                        color_groups[group_color] += 1
                        found_group = True
                        break

                if not found_group:
                    color_groups[tuple(pixel)] = 1

            # Convert to dominant colors format
            total_pixels = len(sampled_pixels)
            dominant_colors = []

            for color, count in sorted(color_groups.items(), key=lambda x: x[1], reverse=True)[:5]:
                frequency = count / total_pixels
                hsv = self._rgb_to_hsv_opencv(color)
                dominant_colors.append({
                    'rgb': color,
                    'hsv': hsv,
                    'frequency': frequency
                })

            return dominant_colors

        except Exception as e:
            print(f"❌ Error in basic color extraction: {e}")
            return []

    def _rgb_to_hsv_opencv(self, rgb):
        """Convert RGB to HSV using OpenCV format"""
        try:
            rgb_normalized = np.array(rgb).reshape(1, 1, 3).astype(np.float32) / 255.0
            hsv = cv2.cvtColor(rgb_normalized, cv2.COLOR_RGB2HSV)
            return tuple(hsv[0, 0] * [179, 255, 255])  # Scale to OpenCV HSV range
        except:
            return (0, 0, 0)

    def _identify_bird_type_from_colors(self, dominant_colors):
        """Identify bird type based on dominant colors"""
        if not dominant_colors:
            return "unknown"

        # Define bird color profiles
        bird_profiles = {
            'brown_birds': {
                'hsv_ranges': [([10, 50, 50], [25, 255, 255]), ([5, 30, 80], [15, 180, 200])],
                'rgb_ranges': [([80, 60, 40], [160, 120, 90])]
            },
            'gray_birds': {
                'hsv_ranges': [([0, 0, 50], [180, 30, 200]), ([0, 0, 80], [180, 50, 180])],
                'rgb_ranges': [([60, 60, 60], [140, 140, 140])]
            },
            'black_birds': {
                'hsv_ranges': [([0, 0, 0], [180, 255, 80])],
                'rgb_ranges': [([0, 0, 0], [60, 60, 60])]
            },
            'colorful_birds': {
                'hsv_ranges': [([0, 100, 100], [30, 255, 255]), ([30, 100, 100], [60, 255, 255]),
                              ([60, 100, 100], [120, 255, 255]), ([120, 100, 100], [180, 255, 255])],
                'rgb_ranges': [([100, 0, 0], [255, 100, 100]), ([0, 100, 0], [100, 255, 100]),
                              ([0, 0, 100], [100, 100, 255])]
            }
        }

        # Score each bird type
        type_scores = {}

        for bird_type, profile in bird_profiles.items():
            score = 0

            for color_info in dominant_colors:
                rgb = color_info['rgb']
                hsv = color_info['hsv']
                frequency = color_info['frequency']

                # Check HSV ranges
                for hsv_range in profile['hsv_ranges']:
                    lower, upper = hsv_range
                    if all(lower[i] <= hsv[i] <= upper[i] for i in range(3)):
                        score += frequency * 2

                # Check RGB ranges
                for rgb_range in profile['rgb_ranges']:
                    lower, upper = rgb_range
                    if all(lower[i] <= rgb[i] <= upper[i] for i in range(3)):
                        score += frequency

            type_scores[bird_type] = score

        # Return best match
        if type_scores:
            best_type = max(type_scores, key=type_scores.get)
            best_score = type_scores[best_type]

            if best_score > 0.1:
                return best_type

        return "unknown"

    def _create_intelligent_color_mask(self, img_cv, bird_type, dominant_colors):
        """Create intelligent color mask based on bird type and dominant colors"""
        try:
            hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)
            masks = []

            # Create masks based on bird type
            if bird_type == "brown_birds":
                # Brown bird specific ranges
                ranges = [([10, 50, 50], [25, 255, 255]), ([5, 30, 80], [15, 180, 200])]
            elif bird_type == "gray_birds":
                # Gray bird specific ranges
                ranges = [([0, 0, 50], [180, 30, 200]), ([0, 0, 80], [180, 50, 180])]
            elif bird_type == "black_birds":
                # Black bird specific ranges
                ranges = [([0, 0, 0], [180, 255, 80])]
            elif bird_type == "colorful_birds":
                # Colorful bird ranges
                ranges = [([0, 100, 100], [30, 255, 255]), ([30, 100, 100], [60, 255, 255]),
                         ([60, 100, 100], [120, 255, 255]), ([120, 100, 100], [180, 255, 255])]
            else:
                # Unknown bird - use general ranges
                ranges = [([10, 30, 30], [30, 255, 255]), ([0, 0, 30], [180, 50, 200])]

            # Create masks for bird type ranges
            for lower, upper in ranges:
                mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
                masks.append(mask)

            # Add masks for dominant colors with tolerance
            for color_info in dominant_colors[:3]:  # Top 3 colors
                if color_info['frequency'] > 0.05:  # Only significant colors
                    hsv_color = color_info['hsv']

                    # Adaptive tolerance based on color properties
                    if bird_type == "colorful_birds":
                        tolerance_h, tolerance_sv = 15, 60
                    elif bird_type in ["gray_birds", "black_birds"]:
                        tolerance_h, tolerance_sv = 20, 40
                    else:
                        tolerance_h, tolerance_sv = 12, 50

                    lower = np.array([
                        max(0, hsv_color[0] - tolerance_h),
                        max(0, hsv_color[1] - tolerance_sv),
                        max(0, hsv_color[2] - tolerance_sv)
                    ])
                    upper = np.array([
                        min(179, hsv_color[0] + tolerance_h),
                        min(255, hsv_color[1] + tolerance_sv),
                        min(255, hsv_color[2] + tolerance_sv)
                    ])

                    mask = cv2.inRange(hsv, lower, upper)
                    masks.append(mask)

            # Combine all masks
            if masks:
                combined_mask = masks[0]
                for mask in masks[1:]:
                    combined_mask = cv2.bitwise_or(combined_mask, mask)
            else:
                return None

            # Clean up mask with morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            cleaned_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            cleaned_mask = cv2.morphologyEx(cleaned_mask, cv2.MORPH_OPEN, kernel)

            # Remove small noise
            kernel_small = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            cleaned_mask = cv2.morphologyEx(cleaned_mask, cv2.MORPH_OPEN, kernel_small)

            return cleaned_mask

        except Exception as e:
            print(f"❌ Error creating intelligent color mask: {e}")
            return None

    def _color_segmentation_crop(self, img_cv, img_pil):
        """Color-based segmentation to identify and isolate bird subjects"""
        try:
            print("🎨 Applying color segmentation analysis...")

            # Convert to HSV for better color analysis
            hsv = cv2.cvtColor(img_cv, cv2.COLOR_BGR2HSV)

            # Create multiple masks for different color ranges that birds commonly have
            masks = []

            # Brown/earth tones (common in many birds)
            brown_lower = np.array([10, 50, 50])
            brown_upper = np.array([25, 255, 255])
            masks.append(cv2.inRange(hsv, brown_lower, brown_upper))

            # Gray tones
            gray_lower = np.array([0, 0, 50])
            gray_upper = np.array([180, 50, 200])
            masks.append(cv2.inRange(hsv, gray_lower, gray_upper))

            # Darker colors (black, dark brown)
            dark_lower = np.array([0, 0, 0])
            dark_upper = np.array([180, 255, 80])
            masks.append(cv2.inRange(hsv, dark_lower, dark_upper))

            # Combine all masks
            combined_mask = masks[0]
            for mask in masks[1:]:
                combined_mask = cv2.bitwise_or(combined_mask, mask)

            # Morphological operations to clean up the mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            cleaned_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            cleaned_mask = cv2.morphologyEx(cleaned_mask, cv2.MORPH_OPEN, kernel)

            # Find contours in the mask
            contours, _ = cv2.findContours(cleaned_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                print("⚠️ No color-based contours found")
                return None

            # Filter contours by size and compactness
            valid_contours = []
            min_area = (img_pil.width * img_pil.height) * 0.02

            for contour in contours:
                area = cv2.contourArea(contour)
                if area > min_area:
                    # Calculate compactness (how circular/compact the shape is)
                    perimeter = cv2.arcLength(contour, True)
                    if perimeter > 0:
                        compactness = 4 * np.pi * area / (perimeter * perimeter)
                        if compactness > 0.1:  # Reasonably compact shape
                            valid_contours.append((contour, area))

            if not valid_contours:
                print("⚠️ No valid color-based contours found")
                return None

            # Take the largest valid contour
            valid_contours.sort(key=lambda x: x[1], reverse=True)
            best_contour = valid_contours[0][0]

            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(best_contour)

            # Add intelligent padding
            padding_x = max(20, int(w * 0.1))
            padding_y = max(20, int(h * 0.1))

            x = max(0, x - padding_x)
            y = max(0, y - padding_y)
            w = min(img_pil.width - x, w + 2 * padding_x)
            h = min(img_pil.height - y, h + 2 * padding_y)

            # Ensure reasonable size
            if w < 150 or h < 150:
                print("⚠️ Color-based detection area too small")
                return None

            cropped = img_pil.crop((x, y, x + w, y + h))
            print(f"✅ Color segmentation crop: {w}x{h} at ({x}, {y})")
            return cropped

        except Exception as e:
            print(f"❌ Error in color segmentation: {e}")
            return None

    def _ebird_ui_aware_crop(self, img_cv, img_pil):
        """eBird-specific UI element detection and removal for clean bird image extraction"""
        try:
            print("🐦 Applying eBird UI-aware cropping...")

            height, width = img_cv.shape[:2]

            # Convert to grayscale for UI element detection
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # Detect common eBird UI patterns
            # Look for dark horizontal bars (navigation, controls)
            horizontal_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (width//10, 1))
            horizontal_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, horizontal_kernel)

            # Look for vertical sidebars
            vertical_kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (1, height//10))
            vertical_lines = cv2.morphologyEx(gray, cv2.MORPH_OPEN, vertical_kernel)

            # Combine detected UI elements
            ui_elements = cv2.bitwise_or(horizontal_lines, vertical_lines)

            # Find the main content area by excluding UI elements
            # Start with full image and progressively crop based on UI detection

            # Top crop: Remove header/navigation (typically top 8-15% of eBird pages)
            top_region = gray[0:int(height*0.15), :]
            top_ui_density = np.mean(top_region < 100)  # Dark pixels indicating UI

            if top_ui_density > 0.3:  # Significant UI presence
                top_crop = int(height * 0.12)
            else:
                top_crop = int(height * 0.05)

            # Bottom crop: Remove controls/footer
            bottom_region = gray[int(height*0.85):, :]
            bottom_ui_density = np.mean(bottom_region < 100)

            if bottom_ui_density > 0.3:
                bottom_crop = int(height * 0.88)
            else:
                bottom_crop = int(height * 0.95)

            # Left crop: Remove sidebar
            left_region = gray[:, 0:int(width*0.15)]
            left_ui_density = np.mean(left_region < 100)

            if left_ui_density > 0.4:
                left_crop = int(width * 0.12)
            else:
                left_crop = int(width * 0.03)

            # Right crop: Remove sidebar
            right_region = gray[:, int(width*0.85):]
            right_ui_density = np.mean(right_region < 100)

            if right_ui_density > 0.4:
                right_crop = int(width * 0.88)
            else:
                right_crop = int(width * 0.97)

            # Ensure we have a reasonable crop area
            crop_width = right_crop - left_crop
            crop_height = bottom_crop - top_crop

            if crop_width < width * 0.4 or crop_height < height * 0.4:
                print("⚠️ eBird UI detection would crop too aggressively")
                return None

            # Apply the crop
            cropped = img_pil.crop((left_crop, top_crop, right_crop, bottom_crop))

            print(f"✅ eBird UI-aware crop: {crop_width}x{crop_height}")
            return cropped

        except Exception as e:
            print(f"❌ Error in eBird UI-aware cropping: {e}")
            return None

    def _content_aware_crop(self, img_cv, img_pil):
        """Content-aware cropping using gradient analysis and texture detection"""
        try:
            print("🧠 Applying content-aware analysis...")

            # Convert to grayscale
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)

            # Calculate gradients to find areas of high detail (likely the main subject)
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

            # Normalize gradient magnitude
            gradient_magnitude = cv2.normalize(gradient_magnitude, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)

            # Apply Gaussian blur to create interest map
            interest_map = cv2.GaussianBlur(gradient_magnitude, (21, 21), 0)

            # Find the region with highest interest (most detail)
            # Divide image into grid and calculate average interest for each cell
            grid_size = 8
            height, width = interest_map.shape
            cell_height = height // grid_size
            cell_width = width // grid_size

            max_interest = 0
            best_region = None

            for i in range(grid_size - 2):  # Allow 3x3 region selection
                for j in range(grid_size - 2):
                    # Calculate interest for 3x3 cell region
                    start_y = i * cell_height
                    end_y = (i + 3) * cell_height
                    start_x = j * cell_width
                    end_x = (j + 3) * cell_width

                    region_interest = np.mean(interest_map[start_y:end_y, start_x:end_x])

                    if region_interest > max_interest:
                        max_interest = region_interest
                        best_region = (start_x, start_y, end_x - start_x, end_y - start_y)

            if best_region is None:
                print("⚠️ No high-interest region found")
                return None

            x, y, w, h = best_region

            # Add padding around the high-interest region
            padding_x = max(30, int(w * 0.15))
            padding_y = max(30, int(h * 0.15))

            x = max(0, x - padding_x)
            y = max(0, y - padding_y)
            w = min(img_pil.width - x, w + 2 * padding_x)
            h = min(img_pil.height - y, h + 2 * padding_y)

            # Ensure minimum size
            if w < 200 or h < 200:
                print("⚠️ Content-aware region too small")
                return None

            cropped = img_pil.crop((x, y, x + w, y + h))
            print(f"✅ Content-aware crop: {w}x{h} at ({x}, {y})")
            return cropped

        except Exception as e:
            print(f"❌ Error in content-aware cropping: {e}")
            return None

    def _enhanced_center_crop(self, img_pil):
        """Enhanced center crop with dynamic sizing based on image analysis"""
        try:
            print("📐 Applying enhanced center crop...")

            width, height = img_pil.size

            # Dynamic crop ratio based on image dimensions
            aspect_ratio = width / height

            if aspect_ratio > 1.5:  # Wide image
                crop_ratio_w = 0.8
                crop_ratio_h = 0.9
            elif aspect_ratio < 0.7:  # Tall image
                crop_ratio_w = 0.9
                crop_ratio_h = 0.8
            else:  # Square-ish image
                crop_ratio_w = 0.75
                crop_ratio_h = 0.75

            crop_width = int(width * crop_ratio_w)
            crop_height = int(height * crop_ratio_h)

            # Slightly offset center towards upper portion (birds often in upper-center)
            left = (width - crop_width) // 2
            top = int((height - crop_height) * 0.4)  # 40% from top instead of 50%
            right = left + crop_width
            bottom = top + crop_height

            # Ensure bounds
            top = max(0, top)
            bottom = min(height, bottom)
            left = max(0, left)
            right = min(width, right)

            cropped = img_pil.crop((left, top, right, bottom))
            print(f"✅ Enhanced center crop: {crop_width}x{crop_height}")
            return cropped

        except Exception as e:
            print(f"❌ Error in enhanced center cropping: {e}")
            return None

    def _assess_crop_quality(self, cropped_img, original_img):
        """Assess the quality of a cropped image using multiple metrics"""
        try:
            # Convert to numpy arrays for analysis
            cropped_array = np.array(cropped_img)
            original_array = np.array(original_img)

            # Convert to grayscale for analysis
            if len(cropped_array.shape) == 3:
                cropped_gray = cv2.cvtColor(cropped_array, cv2.COLOR_RGB2GRAY)
            else:
                cropped_gray = cropped_array

            # Metric 1: Edge density (more edges = more detail = better crop)
            edges = cv2.Canny(cropped_gray, 50, 150)
            edge_density = np.sum(edges > 0) / (cropped_gray.shape[0] * cropped_gray.shape[1])
            edge_score = min(edge_density * 1000, 100)  # Normalize to 0-100

            # Metric 2: Contrast (higher contrast = better defined subject)
            contrast = cropped_gray.std()
            contrast_score = min(contrast / 2.55, 100)  # Normalize to 0-100

            # Metric 3: Size retention (larger crops are generally better if they contain the subject)
            size_ratio = (cropped_img.width * cropped_img.height) / (original_img.width * original_img.height)
            size_score = min(size_ratio * 200, 100)  # Favor crops that retain reasonable size

            # Metric 4: Aspect ratio reasonableness (avoid extremely thin crops)
            aspect_ratio = cropped_img.width / cropped_img.height
            if 0.5 <= aspect_ratio <= 2.0:
                aspect_score = 100
            elif 0.3 <= aspect_ratio <= 3.0:
                aspect_score = 70
            else:
                aspect_score = 30

            # Metric 5: Center bias (subjects near center are often better)
            center_x = cropped_img.width / 2
            center_y = cropped_img.height / 2
            # This is a simplified center bias - in a real implementation,
            # you'd analyze where the main subject is located
            center_score = 80  # Default good score for center-focused crops

            # Weighted combination of scores
            weights = {
                'edge': 0.25,
                'contrast': 0.25,
                'size': 0.20,
                'aspect': 0.15,
                'center': 0.15
            }

            total_score = (
                edge_score * weights['edge'] +
                contrast_score * weights['contrast'] +
                size_score * weights['size'] +
                aspect_score * weights['aspect'] +
                center_score * weights['center']
            )

            return total_score

        except Exception as e:
            print(f"❌ Error assessing crop quality: {e}")
            return 50  # Default middle score

    def _enhance_cropped_image(self, cropped_img):
        """Apply post-processing enhancements to improve the cropped image quality"""
        try:
            # Convert to numpy array
            img_array = np.array(cropped_img)

            # Convert to OpenCV format
            img_cv = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)

            # Enhancement 1: Adaptive histogram equalization for better contrast
            lab = cv2.cvtColor(img_cv, cv2.COLOR_BGR2LAB)
            l, a, b = cv2.split(lab)

            clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
            l = clahe.apply(l)

            enhanced = cv2.merge([l, a, b])
            enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2BGR)

            # Enhancement 2: Slight sharpening
            kernel = np.array([[-1,-1,-1],
                              [-1, 9,-1],
                              [-1,-1,-1]])
            sharpened = cv2.filter2D(enhanced, -1, kernel * 0.1)

            # Enhancement 3: Noise reduction while preserving edges
            denoised = cv2.bilateralFilter(sharpened, 9, 75, 75)

            # Convert back to PIL format
            enhanced_rgb = cv2.cvtColor(denoised, cv2.COLOR_BGR2RGB)
            enhanced_pil = Image.fromarray(enhanced_rgb)

            return enhanced_pil

        except Exception as e:
            print(f"❌ Error enhancing cropped image: {e}")
            return cropped_img  # Return original if enhancement fails

    def _debug_problematic_case(self, original_img, cropped_img, debug_dir, metadata):
        """Special debugging for problematic cropping cases"""
        try:
            print(f"🚨 DEBUGGING PROBLEMATIC CASE")
            print(f"=" * 50)

            # Analyze original image characteristics
            img_array = np.array(original_img)
            gray = cv2.cvtColor(img_array, cv2.COLOR_RGB2GRAY)

            # Image statistics
            stats = {
                "mean_brightness": float(np.mean(gray)),
                "std_brightness": float(np.std(gray)),
                "min_brightness": int(np.min(gray)),
                "max_brightness": int(np.max(gray)),
                "image_variance": float(np.var(gray)),
                "edge_density": self._calculate_edge_density(gray),
                "color_distribution": self._analyze_color_distribution(img_array)
            }

            print(f"📊 Image Analysis:")
            for key, value in stats.items():
                print(f"   • {key}: {value}")

            # Test each boundary detection method individually
            print(f"\n🔍 Individual Method Analysis:")

            # Test boundary detection components
            boundaries_1 = self._scan_edges_for_content(gray, 85)
            boundaries_2 = self._gradient_boundary_detection(gray)
            boundaries_3 = self._variance_boundary_detection(gray)

            print(f"   • Edge scanning: {boundaries_1}")
            print(f"   • Gradient detection: {boundaries_2}")
            print(f"   • Variance detection: {boundaries_3}")

            # Create visualization of detected boundaries
            self._create_boundary_visualization(original_img, debug_dir, [
                ("edge_scan", boundaries_1),
                ("gradient", boundaries_2),
                ("variance", boundaries_3)
            ])

            # Analyze crop quality issues
            crop_issues = self._analyze_crop_issues(original_img, cropped_img)
            print(f"\n⚠️ Potential Issues:")
            for issue in crop_issues:
                print(f"   • {issue}")

            # Save detailed analysis
            analysis_data = {
                "session_id": metadata.get("session_id"),
                "image_stats": stats,
                "boundary_results": {
                    "edge_scan": boundaries_1,
                    "gradient": boundaries_2,
                    "variance": boundaries_3
                },
                "crop_issues": crop_issues,
                "recommendations": self._generate_crop_recommendations(stats, crop_issues)
            }

            import json
            analysis_path = os.path.join(debug_dir, "detailed_analysis.json")
            with open(analysis_path, 'w') as f:
                json.dump(analysis_data, f, indent=2, default=str)

            print(f"📁 Detailed analysis saved to: {analysis_path}")
            print(f"=" * 50)

        except Exception as e:
            print(f"❌ Error in problematic case debugging: {e}")

    def _boundary_detection_crop(self, img_cv, img_pil):
        """Advanced boundary detection and cropping using white/background border analysis"""
        try:
            print("🔍 Applying boundary detection analysis...")

            # Convert to grayscale for boundary analysis
            gray = cv2.cvtColor(img_cv, cv2.COLOR_BGR2GRAY)
            h, w = gray.shape

            # Dynamic threshold based on image characteristics
            # Analyze the image to determine appropriate threshold
            mean_brightness = np.mean(gray)
            std_brightness = np.std(gray)

            # Adaptive threshold: if image is generally dark, use lower threshold
            if mean_brightness < 100:
                white_threshold = max(60, mean_brightness + std_brightness * 0.5)
            else:
                white_threshold = max(85, mean_brightness - std_brightness * 0.3)

            print(f"   📊 Using adaptive threshold: {white_threshold:.1f}")

            # Find boundaries using multiple detection methods
            boundaries = self._detect_content_boundaries(gray, white_threshold)

            if boundaries is None:
                print("⚠️ No valid boundaries detected")
                return None

            left_border, right_border, top_border, bottom_border = boundaries

            # Validate boundary detection results
            crop_width = right_border - left_border
            crop_height = bottom_border - top_border

            # Ensure minimum crop size
            min_width = w * 0.3
            min_height = h * 0.3

            if crop_width < min_width or crop_height < min_height:
                print(f"⚠️ Detected boundaries too small: {crop_width}x{crop_height}")
                return None

            # Apply intelligent padding to boundaries
            padding_x = max(10, int(crop_width * 0.02))
            padding_y = max(10, int(crop_height * 0.02))

            # Adjust boundaries with padding and bounds checking
            left_border = max(0, left_border - padding_x)
            right_border = min(w, right_border + padding_x)
            top_border = max(0, top_border - padding_y)
            bottom_border = min(h, bottom_border + padding_y)

            # Final crop dimensions
            final_width = right_border - left_border
            final_height = bottom_border - top_border

            # Crop the image
            cropped = img_pil.crop((left_border, top_border, right_border, bottom_border))

            print(f"✅ Boundary detection crop: {final_width}x{final_height} at ({left_border}, {top_border})")
            return cropped

        except Exception as e:
            print(f"❌ Error in boundary detection cropping: {e}")
            return None

    def _detect_content_boundaries(self, gray, white_threshold):
        """Detect content boundaries using multiple scanning methods"""
        try:
            h, w = gray.shape

            # Method 1: Standard edge scanning
            boundaries_1 = self._scan_edges_for_content(gray, white_threshold)

            # Method 2: Gradient-based boundary detection
            boundaries_2 = self._gradient_boundary_detection(gray)

            # Method 3: Variance-based content detection
            boundaries_3 = self._variance_boundary_detection(gray)

            # Combine results from multiple methods
            valid_boundaries = [b for b in [boundaries_1, boundaries_2, boundaries_3] if b is not None]

            if not valid_boundaries:
                return None

            # Take the most conservative (largest) boundary from all methods
            left_borders = [b[0] for b in valid_boundaries]
            right_borders = [b[1] for b in valid_boundaries]
            top_borders = [b[2] for b in valid_boundaries]
            bottom_borders = [b[3] for b in valid_boundaries]

            # Use the boundaries that capture the most content
            final_left = min(left_borders)
            final_right = max(right_borders)
            final_top = min(top_borders)
            final_bottom = max(bottom_borders)

            return (final_left, final_right, final_top, final_bottom)

        except Exception as e:
            print(f"❌ Error detecting content boundaries: {e}")
            return None

    def _scan_edges_for_content(self, gray, white_threshold):
        """Scan edges to find content boundaries based on brightness threshold"""
        try:
            h, w = gray.shape

            left_border = 0
            right_border = w - 1
            top_border = 0
            bottom_border = h - 1

            # Find left border - scan from left to right
            for i in range(w):
                col = gray[:, i]
                if np.mean(col) < white_threshold:  # Found non-background content
                    left_border = i
                    break

            # Find right border - scan from right to left
            for i in range(w - 1, -1, -1):
                col = gray[:, i]
                if np.mean(col) < white_threshold:
                    right_border = i
                    break

            # Find top border - scan from top to bottom
            for i in range(h):
                row = gray[i, :]
                if np.mean(row) < white_threshold:
                    top_border = i
                    break

            # Find bottom border - scan from bottom to top
            for i in range(h - 1, -1, -1):
                row = gray[i, :]
                if np.mean(row) < white_threshold:
                    bottom_border = i
                    break

            # Validate boundaries
            if (right_border <= left_border or bottom_border <= top_border):
                return None

            return (left_border, right_border, top_border, bottom_border)

        except Exception as e:
            print(f"❌ Error in edge scanning: {e}")
            return None

    def _gradient_boundary_detection(self, gray):
        """Use gradient analysis to detect content boundaries"""
        try:
            h, w = gray.shape

            # Calculate gradients
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

            # Threshold for significant gradients
            grad_threshold = np.mean(gradient_magnitude) + np.std(gradient_magnitude)

            # Find boundaries based on gradient activity
            # Scan for first/last rows/columns with significant gradient activity

            # Top boundary
            top_border = 0
            for i in range(h):
                if np.max(gradient_magnitude[i, :]) > grad_threshold:
                    top_border = max(0, i - 5)  # Small buffer
                    break

            # Bottom boundary
            bottom_border = h - 1
            for i in range(h - 1, -1, -1):
                if np.max(gradient_magnitude[i, :]) > grad_threshold:
                    bottom_border = min(h - 1, i + 5)  # Small buffer
                    break

            # Left boundary
            left_border = 0
            for i in range(w):
                if np.max(gradient_magnitude[:, i]) > grad_threshold:
                    left_border = max(0, i - 5)  # Small buffer
                    break

            # Right boundary
            right_border = w - 1
            for i in range(w - 1, -1, -1):
                if np.max(gradient_magnitude[:, i]) > grad_threshold:
                    right_border = min(w - 1, i + 5)  # Small buffer
                    break

            # Validate boundaries
            if (right_border <= left_border or bottom_border <= top_border):
                return None

            return (left_border, right_border, top_border, bottom_border)

        except Exception as e:
            print(f"❌ Error in gradient boundary detection: {e}")
            return None

    def _variance_boundary_detection(self, gray):
        """Use variance analysis to detect content boundaries"""
        try:
            h, w = gray.shape

            # Calculate variance in sliding windows
            window_size = 20
            variance_threshold = np.var(gray) * 0.3  # 30% of overall image variance

            # Top boundary - find first row with significant variance
            top_border = 0
            for i in range(0, h - window_size, 5):
                window = gray[i:i+window_size, :]
                if np.var(window) > variance_threshold:
                    top_border = max(0, i - 10)
                    break

            # Bottom boundary - find last row with significant variance
            bottom_border = h - 1
            for i in range(h - window_size, 0, -5):
                window = gray[i:i+window_size, :]
                if np.var(window) > variance_threshold:
                    bottom_border = min(h - 1, i + window_size + 10)
                    break

            # Left boundary - find first column with significant variance
            left_border = 0
            for i in range(0, w - window_size, 5):
                window = gray[:, i:i+window_size]
                if np.var(window) > variance_threshold:
                    left_border = max(0, i - 10)
                    break

            # Right boundary - find last column with significant variance
            right_border = w - 1
            for i in range(w - window_size, 0, -5):
                window = gray[:, i:i+window_size]
                if np.var(window) > variance_threshold:
                    right_border = min(w - 1, i + window_size + 10)
                    break

            # Validate boundaries
            if (right_border <= left_border or bottom_border <= top_border):
                return None

            return (left_border, right_border, top_border, bottom_border)

        except Exception as e:
            print(f"❌ Error in variance boundary detection: {e}")
            return None

    def _calculate_edge_density(self, gray_img):
        """Calculate edge density for image analysis"""
        try:
            edges = cv2.Canny(gray_img, 50, 150)
            total_pixels = gray_img.shape[0] * gray_img.shape[1]
            edge_pixels = np.sum(edges > 0)
            return float(edge_pixels / total_pixels)
        except:
            return 0.0

    def _analyze_color_distribution(self, img_array):
        """Analyze color distribution in the image"""
        try:
            # Convert to HSV for better color analysis
            hsv = cv2.cvtColor(img_array, cv2.COLOR_RGB2HSV)

            # Analyze hue distribution
            hue = hsv[:, :, 0]
            hue_hist = cv2.calcHist([hue], [0], None, [180], [0, 180])

            # Find dominant colors
            dominant_hues = np.argsort(hue_hist.flatten())[-5:]  # Top 5 hues

            return {
                "dominant_hues": [int(h) for h in dominant_hues],
                "hue_variance": float(np.var(hue)),
                "saturation_mean": float(np.mean(hsv[:, :, 1])),
                "value_mean": float(np.mean(hsv[:, :, 2]))
            }
        except:
            return {"error": "Could not analyze color distribution"}

    def _create_boundary_visualization(self, original_img, debug_dir, boundary_results):
        """Create visualization of detected boundaries"""
        try:
            from PIL import ImageDraw

            for method_name, boundaries in boundary_results:
                if boundaries:
                    left, right, top, bottom = boundaries

                    # Create visualization
                    vis_img = original_img.copy()
                    draw = ImageDraw.Draw(vis_img)

                    # Draw detected boundaries
                    draw.rectangle([left, top, right, bottom], outline="red", width=3)

                    # Save visualization
                    vis_path = os.path.join(debug_dir, f"boundaries_{method_name}.png")
                    vis_img.save(vis_path)

                    print(f"   📊 {method_name} boundaries visualized: {vis_path}")
        except Exception as e:
            print(f"❌ Error creating boundary visualization: {e}")

    def _analyze_crop_issues(self, original_img, cropped_img):
        """Analyze potential issues with the crop"""
        issues = []

        try:
            # Size analysis
            orig_area = original_img.width * original_img.height
            crop_area = cropped_img.width * cropped_img.height
            size_ratio = crop_area / orig_area

            if size_ratio < 0.1:
                issues.append("Crop is very small (< 10% of original)")
            elif size_ratio > 0.9:
                issues.append("Crop is very large (> 90% of original)")

            # Aspect ratio analysis
            orig_aspect = original_img.width / original_img.height
            crop_aspect = cropped_img.width / cropped_img.height

            if abs(orig_aspect - crop_aspect) > 1.0:
                issues.append("Significant aspect ratio change")

            # Position analysis
            if cropped_img.width < 200 or cropped_img.height < 200:
                issues.append("Crop dimensions too small for quality bird image")

            # Quality analysis
            crop_array = np.array(cropped_img)
            if len(crop_array.shape) == 3:
                crop_gray = cv2.cvtColor(crop_array, cv2.COLOR_RGB2GRAY)
                crop_variance = np.var(crop_gray)

                if crop_variance < 100:
                    issues.append("Low detail/contrast in cropped area")

        except Exception as e:
            issues.append(f"Error analyzing crop: {e}")

        return issues

    def _generate_crop_recommendations(self, stats, issues):
        """Generate recommendations for improving cropping"""
        recommendations = []

        try:
            # Based on brightness
            if stats["mean_brightness"] > 200:
                recommendations.append("Image is very bright - consider adjusting threshold for boundary detection")
            elif stats["mean_brightness"] < 50:
                recommendations.append("Image is very dark - may need different edge detection parameters")

            # Based on variance
            if stats["image_variance"] < 100:
                recommendations.append("Low image variance - try color-based segmentation instead")

            # Based on edge density
            if stats["edge_density"] < 0.05:
                recommendations.append("Low edge density - consider UI-aware cropping or center crop")

            # Based on issues
            if "Crop is very small" in str(issues):
                recommendations.append("Try less aggressive boundary detection or use center crop")

            if "Low detail/contrast" in str(issues):
                recommendations.append("Consider using content-aware cropping for better subject detection")

        except:
            recommendations.append("Could not generate specific recommendations")

        return recommendations

    def _return_to_main_page(self, main_page_url=None, max_retries=3):
        """Enhanced navigation back to main gallery page with comprehensive error handling"""
        print(f"🔄 Returning to main gallery page...")

        for attempt in range(max_retries):
            try:
                print(f"   Attempt {attempt + 1}/{max_retries}")

                # Step 1: Handle multiple windows/tabs
                if len(self.wd.window_handles) > 1:
                    print("   📱 Closing additional tabs/windows...")
                    # Close all additional windows except the first one
                    main_window = self.wd.window_handles[0]
                    for handle in self.wd.window_handles[1:]:
                        try:
                            self.wd.switch_to.window(handle)
                            self.wd.close()
                        except:
                            pass

                    # Switch back to main window
                    self.wd.switch_to.window(main_window)
                    time.sleep(2)

                # Step 2: Close any modal/lightbox/overlay
                if self._close_modal_or_overlay():
                    print("   ✅ Modal/overlay closed successfully")
                    time.sleep(2)

                # Step 3: Verify we're on the main page or navigate back
                if not self._verify_main_page():
                    print("   🌐 Not on main page, navigating back...")
                    if main_page_url:
                        self.wd.get(main_page_url)
                        time.sleep(3)
                    else:
                        # Try browser back button
                        self.wd.back()
                        time.sleep(3)

                # Step 4: Wait for gallery to load
                if self._wait_for_gallery_ready():
                    print("   ✅ Successfully returned to main gallery page")
                    return True
                else:
                    print(f"   ⚠️ Gallery not ready on attempt {attempt + 1}")

            except Exception as e:
                print(f"   ❌ Error on attempt {attempt + 1}: {e}")
                time.sleep(2)

        print("   ❌ Failed to return to main page after all attempts")
        return False

    def _close_modal_or_overlay(self):
        """Close any modal, lightbox, or overlay - prioritize X button over ESC"""
        try:
            print("   🔄 Attempting to close modal/overlay...")

            # Method 1: Look for X/close buttons FIRST (more reliable than ESC)
            close_selectors = [
                # X button patterns (highest priority)
                "button[aria-label*='close' i]",
                "button[title*='close' i]",
                "button[aria-label*='×']",
                "button:contains('×')",
                "button:contains('✕')",
                "[role='button'][aria-label*='close' i]",

                # Standard close buttons
                ".close", ".modal-close", ".lightbox-close", ".overlay-close",
                "[data-dismiss='modal']",

                # Icon-based close buttons
                ".fa-times", ".fa-close", ".icon-close", ".icon-x",
                ".close-icon", ".x-icon",

                # eBird specific close buttons
                "[data-testid='close-button']", "[data-testid='modal-close']",
                ".MediaViewer-close", ".FullscreenImage-close",

                # More generic X patterns
                "button[class*='close']",
                "button[class*='x-']",
                "[class*='close-btn']",
                "[class*='close-button']"
            ]

            for selector in close_selectors:
                try:
                    # Handle :contains() pseudo-selector manually
                    if ':contains(' in selector:
                        text_to_find = selector.split(':contains(\'')[1].split('\')')[0]
                        buttons = self.wd.find_elements(By.TAG_NAME, 'button')
                        for button in buttons:
                            if text_to_find in button.text and button.is_displayed() and button.is_enabled():
                                print(f"   🔘 Found X button with text: '{button.text}'")
                                button.click()
                                time.sleep(2)
                                print(f"   ✅ Successfully clicked X button")
                                return True
                    else:
                        close_elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                        for element in close_elements:
                            if element.is_displayed() and element.is_enabled():
                                print(f"   🔘 Found close element: {selector}")
                                element.click()
                                time.sleep(2)
                                print(f"   ✅ Successfully clicked close element")
                                return True
                except Exception as e:
                    continue

            # Method 2: Try ESC key as fallback only
            print("   🔑 No X button found, trying ESC key as fallback...")
            try:
                from selenium.webdriver.common.keys import Keys
                self.wd.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                time.sleep(2)
                print("   🔑 ESC key pressed")
                return True
            except Exception as e:
                print(f"   ⚠️ ESC key failed: {e}")

            # Method 3: Click outside modal (on overlay background) as last resort
            print("   🎯 Trying to click overlay background...")
            try:
                overlay_selectors = [
                    ".modal-backdrop", ".overlay-background", ".lightbox-backdrop",
                    ".modal-overlay", "[data-testid='overlay']"
                ]

                for selector in overlay_selectors:
                    try:
                        overlay = self.wd.find_element(By.CSS_SELECTOR, selector)
                        if overlay.is_displayed():
                            overlay.click()
                            time.sleep(2)
                            print(f"   ✅ Clicked overlay background: {selector}")
                            return True
                    except:
                        continue
            except:
                pass

            print("   ❌ All close methods failed")
            return False

        except Exception as e:
            print(f"   ❌ Error closing modal/overlay: {e}")
            return False

    def _verify_main_page(self):
        """Verify that we're on the main eBird catalog page"""
        try:
            # Check URL
            current_url = self.wd.current_url
            if 'catalog' in current_url and 'ebird.org' in current_url:
                # Check for gallery elements
                gallery_indicators = [
                    ".MediaCard", ".MediaThumbnail", "[data-testid='media-card']",
                    "img[src*='macaulaylibrary.org']", ".media-grid", ".photo-grid"
                ]

                for selector in gallery_indicators:
                    try:
                        elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                        if len(elements) > 0:
                            print(f"   ✅ Found gallery elements: {len(elements)} items")
                            return True
                    except:
                        continue

            return False

        except Exception as e:
            print(f"   ⚠️ Error verifying main page: {e}")
            return False

    def _wait_for_gallery_ready(self, timeout=10):
        """Wait for the image gallery to be ready for interaction"""
        try:
            from selenium.webdriver.support.ui import WebDriverWait
            from selenium.webdriver.support import expected_conditions as EC

            # Wait for gallery elements to be present and visible
            gallery_selectors = [
                ".MediaCard img", ".MediaThumbnail img",
                "[data-testid='media-card'] img", "img[src*='macaulaylibrary.org']"
            ]

            for selector in gallery_selectors:
                try:
                    WebDriverWait(self.wd, timeout).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )

                    # Additional wait for elements to be clickable
                    time.sleep(2)

                    # Verify elements are actually visible and clickable
                    elements = self.wd.find_elements(By.CSS_SELECTOR, selector)
                    clickable_count = 0
                    for element in elements[:5]:  # Check first 5 elements
                        try:
                            if element.is_displayed() and element.is_enabled():
                                clickable_count += 1
                        except:
                            pass

                    if clickable_count > 0:
                        print(f"   ✅ Gallery ready: {clickable_count} clickable images found")
                        return True

                except:
                    continue

            print("   ⚠️ Gallery not ready: no clickable images found")
            return False

        except Exception as e:
            print(f"   ❌ Error waiting for gallery: {e}")
            return False

    def _get_image_urls_from_page(self):
        """Ekstrak URL gambar dengan metode klik dan lihat (DEPRECATED - gunakan _click_and_download_images)"""
        # Method lama untuk backward compatibility
        return self._click_and_download_images(max_images=50, download_method='url_only')

    def _convert_to_high_res(self, img_url):
        """Enhanced URL conversion untuk mendapatkan resolusi maksimal"""
        if not img_url:
            return None

        try:
            original_url = img_url
            print(f"🔍 Converting URL to highest resolution: {img_url[:100]}...")

            if 'macaulaylibrary.org' in img_url:
                # Strategi untuk Macaulay Library - coba berbagai parameter resolusi tinggi
                base_url = img_url.split('?')[0]  # Hapus semua parameter

                # Coba berbagai parameter untuk resolusi maksimal
                high_res_params = [
                    '?size=original',      # Resolusi asli
                    '?size=full',          # Full size
                    '?size=xlarge',        # Extra large
                    '?size=2400',          # Specific size
                    '?size=1920',          # HD size
                    '?width=2400',         # Width parameter
                    '?w=2400&h=1600',      # Width & height
                    ''                     # No parameters (sometimes gives full size)
                ]

                # Test each parameter to find the largest image
                for param in high_res_params:
                    test_url = base_url + param
                    if self._test_image_url_size(test_url):
                        img_url = test_url
                        break

            elif 'cdn.download.ams.birds.cornell.edu' in img_url:
                # Strategi untuk Cornell CDN
                img_url = img_url.replace('/thumbnails/', '/catalog/')
                img_url = img_url.replace('_thumbnail', '')
                img_url = img_url.replace('_small', '')
                img_url = img_url.replace('_medium', '')
                img_url = img_url.replace('_large', '')  # Remove size restrictions

                # Coba tambahkan parameter resolusi tinggi
                if '?' not in img_url:
                    img_url += '?size=original'

            # Coba pattern lain untuk eBird
            elif 'ebird.org' in img_url or 'birds.cornell.edu' in img_url:
                # Remove size restrictions
                img_url = img_url.replace('_s.', '_o.')  # small to original
                img_url = img_url.replace('_m.', '_o.')  # medium to original
                img_url = img_url.replace('_l.', '_o.')  # large to original
                img_url = img_url.replace('_thumb.', '_o.')  # thumbnail to original

                # Add high resolution parameters
                if '?' in img_url:
                    img_url += '&quality=100&size=original'
                else:
                    img_url += '?quality=100&size=original'

            if img_url != original_url:
                print(f"✅ URL converted for higher resolution")
            else:
                print(f"⚠️ No conversion applied, using original URL")

            return img_url

        except Exception as e:
            print(f"Error converting URL to high res: {e}")
            return original_url

    def _test_image_url_size(self, url):
        """Test if an image URL returns a larger file"""
        try:
            import requests
            response = requests.head(url, timeout=5)
            if response.status_code == 200:
                content_length = response.headers.get('content-length')
                if content_length and int(content_length) > 50000:  # > 50KB indicates good quality
                    return True
        except:
            pass
        return False

    def _get_high_res_image_from_detail(self, detail_url):
        """Ambil URL gambar resolusi tinggi dari halaman detail"""
        try:
            # Buka tab baru untuk halaman detail
            self.wd.execute_script("window.open('');")
            self.wd.switch_to.window(self.wd.window_handles[1])

            self.wd.get(detail_url)
            time.sleep(3)

            # Cari gambar resolusi tinggi dengan berbagai selector
            selectors = [
                "img[src*='macaulaylibrary.org']:not([src*='thumbnail'])",
                "img[src*='cdn.download.ams.birds.cornell.edu']:not([src*='thumbnail'])",
                ".MediaDisplay img",
                ".MediaViewer img",
                "[data-testid='media-display'] img",
                "img[alt*='photo']",
                "img[alt*='image']"
            ]

            img_url = None
            for selector in selectors:
                try:
                    high_res_img = self.wd.find_element(By.CSS_SELECTOR, selector)
                    img_url = high_res_img.get_attribute('src')
                    if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                        break
                except:
                    continue

            # Tutup tab dan kembali ke tab utama
            self.wd.close()
            self.wd.switch_to.window(self.wd.window_handles[0])

            return self._convert_to_high_res(img_url) if img_url else None

        except Exception as e:
            print(f"Error getting high res image from {detail_url}: {e}")
            # Jika ada error, tutup tab dan kembali ke tab utama
            try:
                if len(self.wd.window_handles) > 1:
                    self.wd.close()
                    self.wd.switch_to.window(self.wd.window_handles[0])
            except:
                pass
            return None

    def _download_image(self, img_url, filename, output_dir):
        """Download image using JavaScript and browser capabilities"""
        try:
            print(f"📥 Downloading image via JavaScript: {filename}")

            # Create full path
            filepath = os.path.join(output_dir, filename)

            # Method 1: JavaScript download (best for authenticated content)
            try:
                download_script = f"""
                async function downloadImageViaJS() {{
                    try {{
                        console.log('Starting JavaScript download for: {img_url}');

                        // Fetch the image with current browser session
                        const response = await fetch('{img_url}');
                        if (!response.ok) {{
                            throw new Error('Network response was not ok: ' + response.status);
                        }}

                        // Get the blob
                        const blob = await response.blob();
                        console.log('Image blob size:', blob.size);

                        if (blob.size < 1000) {{
                            throw new Error('Image too small: ' + blob.size + ' bytes');
                        }}

                        // Create download link
                        const link = document.createElement('a');
                        link.href = URL.createObjectURL(blob);
                        link.download = '{filename}';
                        link.style.display = 'none';

                        // Trigger download
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        // Clean up
                        setTimeout(() => URL.revokeObjectURL(link.href), 1000);

                        return blob.size;
                    }} catch (error) {{
                        console.error('JavaScript download failed:', error);
                        return 0;
                    }}
                }}

                return downloadImageViaJS();
                """

                blob_size = self.wd.execute_script(download_script)

                if blob_size and blob_size > 1000:  # At least 1KB
                    print(f"✅ JavaScript download successful (size: {blob_size} bytes)")
                    time.sleep(2)  # Wait for download to complete
                    return True
                else:
                    print(f"⚠️ JavaScript download failed or file too small (size: {blob_size})")

            except Exception as js_error:
                print(f"⚠️ JavaScript download error: {js_error}")

            # Method 2: Fallback to requests with browser cookies
            try:
                print(f"🔄 Trying fallback requests download...")

                # Get cookies from browser
                cookies = {}
                for cookie in self.wd.get_cookies():
                    cookies[cookie['name']] = cookie['value']

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                    'Referer': self.wd.current_url,
                    'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8'
                }

                response = requests.get(img_url, headers=headers, cookies=cookies, stream=True, timeout=30)
                response.raise_for_status()

                # Save file
                with open(filepath, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        f.write(chunk)

                # Verify file was created and has content
                if os.path.exists(filepath) and os.path.getsize(filepath) > 1000:  # At least 1KB
                    file_size = os.path.getsize(filepath)
                    print(f"✅ Fallback download successful: {filename} ({file_size} bytes)")
                    return True
                else:
                    print(f"❌ Downloaded file is too small or doesn't exist")
                    return False

            except Exception as req_error:
                print(f"❌ Requests download also failed: {req_error}")
                return False

        except Exception as e:
            print(f"❌ Error in download process: {e}")
            return False

    def _click_and_download_images(self, max_images=50, download_method='url', output_dir=None):
        """Method utama: klik gambar, lihat versi penuh, kemudian download/screenshot"""
        downloaded_items = []

        try:
            # Store original gallery URL before starting
            self._store_original_gallery_url()

            # Cari semua gambar yang bisa diklik
            clickable_images = self._get_clickable_images_from_page()

            if not clickable_images:
                print("No clickable images found")
                return []

            # Batasi jumlah gambar
            if max_images and len(clickable_images) > max_images:
                clickable_images = clickable_images[:max_images]

            print(f"Processing {len(clickable_images)} images with method: {download_method}")

            for i, clickable_img in enumerate(tqdm.tqdm(clickable_images, desc="Processing images")):
                try:
                    # Klik dan dapatkan gambar penuh dengan method yang dipilih
                    result = self._click_and_get_full_image(clickable_img, i, output_dir, download_method, crop_to_bird=False)

                    if result:
                        downloaded_items.append(result)
                        print(f"✅ Successfully processed image {i + 1}: {result}")

                    # Jeda untuk menghindari rate limiting
                    time.sleep(1)

                except Exception as e:
                    print(f"Error processing image {i+1}: {e}")
                    continue

            return downloaded_items

        except Exception as e:
            print(f"Error in click and download process: {e}")
            return []

    def scrape_ebird(self, ebird_url, output_dir, max_images=50, method='click_and_view', timeout_minutes=30, max_load_more_clicks=None, crop_to_bird=True, download_method='url'):
        """Enhanced eBird scraper with robust error handling and bulk processing"""
        print("=" * 60)
        print("🦅 ENHANCED EBIRD SCRAPER STARTING")
        print("=" * 60)
        print(f"📍 URL: {ebird_url}")
        print(f"🎯 Method: {method}")
        print(f"📁 Output: {output_dir}")
        print(f"🔢 Max images: {max_images}")
        print(f"⏱️ Timeout: {timeout_minutes} minutes")
        print(f"📥 Download method: {download_method}")
        if max_load_more_clicks is None:
            print(f"🔄 Load More: Unlimited (until no more buttons)")
        else:
            print(f"🔄 Load More: Limited to {max_load_more_clicks} clicks")
        print("=" * 60)

        start_time = time.time()
        timeout_seconds = timeout_minutes * 60

        # Statistics tracking
        stats = {
            'attempted': 0,
            'successful_downloads': 0,
            'successful_screenshots': 0,
            'failed': 0,
            'errors': []
        }

        try:
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            print(f"✓ Output directory created: {output_dir}")

            # Open eBird page with error handling
            print("\n🌐 Loading eBird page...")
            try:
                self.wd.get(ebird_url)
                time.sleep(3)
                print("✓ Page loaded successfully")
            except Exception as e:
                print(f"✗ Failed to load page: {e}")
                return self._return_error_stats(stats, f"Failed to load page: {e}")

            # Enhanced scroll and load more with progress feedback
            print("\n📜 Loading all available images...")
            try:
                self._scroll_and_load_more(timeout_seconds=min(300, timeout_seconds//2), max_load_more_clicks=max_load_more_clicks)
                print("✓ Page scrolling and loading completed")
            except Exception as e:
                print(f"⚠️ Warning during page loading: {e}")
                stats['errors'].append(f"Page loading warning: {e}")

            downloaded_count = 0

            if method == 'click_and_view':
                downloaded_count = self._process_click_and_view_method(
                    output_dir, max_images, stats, timeout_seconds, start_time, max_load_more_clicks, crop_to_bird, download_method
                )

            # Fallback to direct URL method if needed
            if (method == 'direct_url' or downloaded_count == 0) and (time.time() - start_time) < timeout_seconds:
                print("\n🔄 Using direct URL method as fallback...")
                fallback_count = self._process_direct_url_method(
                    output_dir, max_images, stats, timeout_seconds, start_time
                )
                downloaded_count += fallback_count

        except Exception as e:
            print(f"\n💥 Critical error in scraping process: {e}")
            stats['errors'].append(f"Critical error: {e}")

        finally:
            # Always quit browser
            try:
                self.wd.quit()
            except:
                pass

        # Print final statistics
        self._print_final_stats(stats, start_time, output_dir)
        return stats['successful_downloads'] + stats['successful_screenshots']

    def _process_click_and_view_method(self, output_dir, max_images, stats, timeout_seconds, start_time, max_load_more_clicks=None, crop_to_bird=True, download_method='url'):
        """Process images using simplified click and view method - No ID validation barriers"""
        print("\n🖱️ Using SIMPLIFIED CLICK AND VIEW method...")
        print("📋 Process: 1) Load More → 2) Get Images → 3) Click → 4) Try URL → 5) Fallback Screenshot → 6) Return")

        try:
            # Store original gallery URL before starting
            self._store_original_gallery_url()

            # STEP 1: Load content using Load More
            print("\n🔄 STEP 1: Loading content...")
            self._scroll_and_load_more(timeout_seconds=min(300, timeout_seconds//2), max_load_more_clicks=max_load_more_clicks)

            # STEP 2: Get all clickable images (no ID validation)
            print("\n🔍 STEP 2: Getting clickable images...")
            clickable_images = self._get_clickable_images_from_page()

            if not clickable_images:
                print("❌ No clickable images found")
                return 0

            # Limit images if specified
            if max_images and len(clickable_images) > max_images:
                clickable_images = clickable_images[:max_images]
                print(f"📊 Limited to {max_images} images (from {len(self._get_clickable_images_from_page())} available)")

            print(f"🎯 STEP 3: Processing {len(clickable_images)} images...")

            successful_count = 0

            # STEP 3: Process each image with simplified workflow
            for i, clickable_img in enumerate(clickable_images):
                # Check timeout
                if (time.time() - start_time) > timeout_seconds:
                    print(f"\n⏰ Timeout reached after {successful_count} images")
                    break

                print(f"\n📥 Processing image {i+1}/{len(clickable_images)}")
                stats['attempted'] += 1

                try:
                    # Click and process image (try URL first, fallback to screenshot)
                    result = self._click_and_get_full_image(clickable_img, i, output_dir, download_method, crop_to_bird)

                    if result and os.path.exists(result):
                        # Determine if it's a download or screenshot
                        if result.endswith('.jpg'):
                            stats['successful_downloads'] += 1
                        else:
                            stats['successful_screenshots'] += 1

                        successful_count += 1
                        print(f"✅ Successfully processed image {i+1}: {os.path.basename(result)}")

                    elif result:
                        print(f"⚠️ Processing result but file not found: {result}")
                        stats['failed'] += 1
                    else:
                        print(f"❌ Failed to process image {i+1}")
                        stats['failed'] += 1

                    # Verify we're ready for next image
                    if not self._verify_ready_for_next_image():
                        print(f"⚠️ Page not ready after image {i+1}, attempting recovery...")
                        if not self._recover_page_state():
                            print(f"❌ Failed to recover page state, stopping processing")
                            break

                    print(f"✅ Ready for next image (completed {i+1}/{len(clickable_images)})")

                    # Rate limiting between images
                    time.sleep(2)

                except Exception as e:
                    print(f"❌ Error processing image {i+1}: {e}")
                    stats['errors'].append(f"Image {i+1} error: {e}")
                    stats['failed'] += 1

                    # Try to recover page state after error
                    try:
                        self._recover_page_state()
                    except:
                        pass

            print(f"\n🎊 SIMPLIFIED PROCESSING COMPLETE!")
            print(f"📊 Processed: {len(clickable_images)} images")
            print(f"✅ Successful: {successful_count} files")

            return successful_count

        except Exception as e:
            print(f"❌ Error in simplified click and view method: {e}")
            stats['errors'].append(f"Simplified method error: {e}")
            return 0

    def _process_direct_url_method(self, output_dir, max_images, stats, timeout_seconds, start_time):
        """Process images using direct URL method"""
        try:
            image_urls = self._get_image_urls_old_method()
            print(f"🔗 Found {len(image_urls)} image URLs")

            if not image_urls:
                print("⚠️ No image URLs found")
                return 0

            # Limit images if specified
            if max_images and len(image_urls) > max_images:
                image_urls = image_urls[:max_images]

            successful_count = 0

            for i, img_url in enumerate(tqdm.tqdm(image_urls, desc="⬇️ Downloading images")):
                # Check timeout
                if (time.time() - start_time) > timeout_seconds:
                    print(f"\n⏰ Timeout reached, stopping at image {i+1}")
                    break

                stats['attempted'] += 1
                filename = f"ebird_image_{i+1:03d}.jpg"

                try:
                    if self._download_image(img_url, filename, output_dir):
                        stats['successful_downloads'] += 1
                        successful_count += 1
                        print(f"✓ Downloaded: {filename}")
                    else:
                        stats['failed'] += 1
                        print(f"✗ Failed: {filename}")
                except Exception as e:
                    stats['failed'] += 1
                    error_msg = f"Error downloading {filename}: {e}"
                    print(f"✗ {error_msg}")
                    stats['errors'].append(error_msg)

                time.sleep(0.5)  # Rate limiting

            return successful_count

        except Exception as e:
            error_msg = f"Error in direct URL method: {e}"
            print(f"💥 {error_msg}")
            stats['errors'].append(error_msg)
            return 0

    def _return_error_stats(self, stats, error_message):
        """Return error statistics"""
        stats['errors'].append(error_message)
        self._print_final_stats(stats, time.time(), "N/A")
        return 0

    def _print_final_stats(self, stats, start_time, output_dir):
        """Print comprehensive final statistics"""
        elapsed_time = time.time() - start_time
        total_successful = stats['successful_downloads'] + stats['successful_screenshots']

        print("\n" + "=" * 60)
        print("📊 SCRAPING COMPLETED - FINAL STATISTICS")
        print("=" * 60)
        print(f"⏱️ Total time: {elapsed_time/60:.1f} minutes")
        print(f"🎯 Images attempted: {stats['attempted']}")
        print(f"✅ Total successful: {total_successful}")
        print(f"  📥 Downloads: {stats['successful_downloads']}")
        print(f"  📸 Screenshots: {stats['successful_screenshots']}")
        print(f"❌ Failed: {stats['failed']}")

        if stats['attempted'] > 0:
            success_rate = (total_successful / stats['attempted']) * 100
            print(f"📈 Success rate: {success_rate:.1f}%")

        if total_successful > 0:
            avg_time = elapsed_time / total_successful
            print(f"⚡ Average time per image: {avg_time:.1f} seconds")

        print(f"📁 Output directory: {output_dir}")

        if stats['errors']:
            print(f"\n⚠️ Errors encountered ({len(stats['errors'])}):")
            for i, error in enumerate(stats['errors'][:5], 1):  # Show first 5 errors
                print(f"  {i}. {error}")
            if len(stats['errors']) > 5:
                print(f"  ... and {len(stats['errors']) - 5} more errors")

        print("=" * 60)

    def _get_image_urls_old_method(self):
        """Method lama untuk mengambil URL gambar langsung"""
        image_urls = []

        try:
            # Cari semua elemen gambar dengan berbagai selector
            selectors = [
                "img[src*='macaulaylibrary.org']",
                "img[src*='cdn.download.ams.birds.cornell.edu']",
                "img[data-src*='macaulaylibrary.org']",
                "img[data-src*='cdn.download.ams.birds.cornell.edu']",
                ".MediaCard img",
                ".MediaThumbnail img",
                "[data-testid='media-card'] img"
            ]

            for selector in selectors:
                try:
                    images = self.wd.find_elements(By.CSS_SELECTOR, selector)

                    for img in images:
                        # Coba ambil dari src atau data-src
                        img_url = img.get_attribute('src') or img.get_attribute('data-src')

                        if img_url and any(domain in img_url for domain in ['macaulaylibrary.org', 'cdn.download.ams.birds.cornell.edu']):
                            # Konversi ke URL resolusi tinggi
                            high_res_url = self._convert_to_high_res(img_url)
                            if high_res_url:
                                image_urls.append(high_res_url)

                except Exception as e:
                    continue

            # Hapus duplikat
            unique_urls = list(set(image_urls))
            return unique_urls

        except Exception as e:
            print(f"Error getting image URLs: {e}")
            return []


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Web scraper for Google Images and eBird photos")

    # Tambahkan pilihan mode
    parser.add_argument('--mode', choices=['google', 'ebird'], default='google',
                        help='Pilih mode scraping: google untuk Google Images, ebird untuk eBird')

    # Argumen untuk Google Images (mode lama)
    parser.add_argument('--search', type=str,
                        help='The keyword to search in Google Images')

    parser.add_argument('--min_image_count', type=int, default=1,
                        help='Minimum number of images for Google Images scraping')

    # Argumen untuk eBird
    parser.add_argument('--ebird_url', type=str,
                        help='URL eBird untuk scraping foto (contoh: https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1)')

    parser.add_argument('--max_images', type=int, default=50,
                        help='Maksimum jumlah gambar untuk didownload dari eBird')

    parser.add_argument('--method', choices=['click_and_view', 'direct_url'], default='click_and_view',
                        help='Metode scraping: click_and_view (klik gambar dulu, lihat versi penuh) atau direct_url (ambil URL langsung)')

    parser.add_argument('--timeout', type=int, default=30,
                        help='Timeout dalam menit untuk proses scraping (default: 30 menit)')

    parser.add_argument('--ultra_quality', action='store_true',
                        help='Aktifkan mode ultra high quality untuk resolusi maksimal (lebih lambat tapi kualitas terbaik)')

    parser.add_argument('--max_load_more_clicks', type=int, default=None,
                        help='Maksimum jumlah klik tombol "More results" (default: unlimited). Contoh: --max_load_more_clicks 3 untuk hanya 3 kali klik')

    # Argumen umum
    parser.add_argument('--out_directory', default=os.getcwd(),
                        help='The full path to the output directory')

    parser.add_argument('--headless', action='store_true',
                        help='Jalankan browser dalam mode headless (tanpa GUI)')

    parser.add_argument('--enable_cropping', action='store_true',
                        help='Enable simplified bird image cropping (16:9 aspect ratio)')

    parser.add_argument('--disable_cropping', action='store_true',
                        help='Disable all cropping - keep full screenshots')

    parser.add_argument('--crop_to_bird', action='store_true', default=False,
                        help='Legacy option - use --enable_cropping instead')

    parser.add_argument('--no_crop', action='store_true',
                        help='Legacy option - use --disable_cropping instead')

    parser.add_argument('--download_method', choices=['url', 'screenshot'], default='screenshot',
                        help='Download method: url (download from source URL) or screenshot (take screenshot). URL method gives better quality but may be less reliable.')

    args = parser.parse_args()

    if args.mode == 'google':
        if not args.search:
            print("Error: --search diperlukan untuk mode Google Images")
            exit(1)

        SEARCH = args.search
        IMAGECOUNT = args.min_image_count
        OUTDIR = args.out_directory

        scraperBot = ScraperBot()
        scraperBot.scrape(search=SEARCH, min_image_count=IMAGECOUNT, directory=OUTDIR)

    elif args.mode == 'ebird':
        if not args.ebird_url:
            print("Error: --ebird_url diperlukan untuk mode eBird")
            print("Contoh: python scraperBot.py --mode ebird --ebird_url 'https://media.ebird.org/catalog?birdOnly=true&mediaType=photo&regionCode=ID&taxonCode=javmun1'")
            exit(1)

        ebird_scraper = EBirdScraperBot(headless=args.headless)

        # Set ultra quality mode if requested
        if args.ultra_quality:
            print("🚀 ULTRA QUALITY MODE ACTIVATED!")
            print("⏱️ This will take longer but produce maximum quality results.")
            ebird_scraper.ultra_quality_mode = True

        # Determine cropping setting with new simplified logic
        crop_to_bird = False  # Default: no cropping

        if args.enable_cropping:
            crop_to_bird = True
            print("🔧 Simplified bird cropping ENABLED")
        elif args.disable_cropping:
            crop_to_bird = False
            print("🔧 All cropping DISABLED")
        elif args.crop_to_bird or (hasattr(args, 'no_crop') and not args.no_crop):
            # Legacy support
            crop_to_bird = True
            print("🔧 Legacy cropping mode enabled (consider using --enable_cropping)")
        else:
            print("🔧 Cropping disabled by default (use --enable_cropping to enable)")

        ebird_scraper.scrape_ebird(
            ebird_url=args.ebird_url,
            output_dir=args.out_directory,
            max_images=args.max_images,
            method=args.method,
            timeout_minutes=args.timeout,
            max_load_more_clicks=args.max_load_more_clicks,
            crop_to_bird=crop_to_bird,
            download_method=args.download_method
        )