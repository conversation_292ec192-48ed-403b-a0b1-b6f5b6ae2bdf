#!/usr/bin/env python3
"""
Comprehensive Testing Suite for Bird Cropping Bot
================================================

This script provides comprehensive testing functionality for the bird cropping bot,
including unit tests, integration tests, and performance benchmarks.

Author: AI Assistant
Version: 1.0.0
"""

import unittest
import os
import sys
import time
import tempfile
import shutil
from pathlib import Path
import cv2
import numpy as np
import json
from typing import List, Dict

# Import the cropping bot modules
from bird_cropping_bot import (
    IntelligentCropper, CroppingConfig, SaliencyDetector, 
    ContentAnalyzer, load_config, save_default_config
)
from batch_crop import AdvancedBatchProcessor
from create_sample_images import SampleImageGenerator

class TestCroppingConfig(unittest.TestCase):
    """Test configuration management"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, "test_config.json")
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_default_config_creation(self):
        """Test default configuration creation"""
        config = CroppingConfig()
        self.assertEqual(config.saliency_method, "spectral_residual")
        self.assertEqual(config.output_aspect_ratio, "16:9")
        self.assertTrue(config.enable_face_detection)
    
    def test_config_save_load(self):
        """Test configuration save and load"""
        save_default_config(self.config_path)
        self.assertTrue(os.path.exists(self.config_path))
        
        loaded_config = load_config(self.config_path)
        self.assertIsInstance(loaded_config, CroppingConfig)
        self.assertEqual(loaded_config.saliency_method, "spectral_residual")


class TestSaliencyDetector(unittest.TestCase):
    """Test saliency detection functionality"""
    
    def setUp(self):
        self.detector = SaliencyDetector("spectral_residual")
        # Create test image
        self.test_image = np.random.randint(0, 255, (300, 400, 3), dtype=np.uint8)
        # Add a bright region to make saliency detection easier
        cv2.rectangle(self.test_image, (150, 100), (250, 200), (255, 255, 255), -1)
    
    def test_saliency_detector_initialization(self):
        """Test saliency detector initialization"""
        self.assertIsNotNone(self.detector.detector)
    
    def test_saliency_map_computation(self):
        """Test saliency map computation"""
        saliency_map = self.detector.compute_saliency_map(self.test_image)
        if saliency_map is not None:
            self.assertEqual(saliency_map.shape[:2], self.test_image.shape[:2])
            self.assertEqual(saliency_map.dtype, np.uint8)
    
    def test_different_saliency_methods(self):
        """Test different saliency detection methods"""
        methods = ["spectral_residual", "fine_grained", "bing"]
        for method in methods:
            detector = SaliencyDetector(method)
            saliency_map = detector.compute_saliency_map(self.test_image)
            # Some methods might not be available, so we just check they don't crash
            if saliency_map is not None:
                self.assertIsInstance(saliency_map, np.ndarray)


class TestContentAnalyzer(unittest.TestCase):
    """Test content analysis functionality"""
    
    def setUp(self):
        self.config = CroppingConfig()
        self.analyzer = ContentAnalyzer(self.config)
        # Create test image with distinct regions
        self.test_image = np.zeros((300, 400, 3), dtype=np.uint8)
        # Add colored regions
        cv2.rectangle(self.test_image, (50, 50), (150, 150), (139, 69, 19), -1)  # Brown
        cv2.rectangle(self.test_image, (200, 100), (350, 200), (34, 139, 34), -1)  # Green
    
    def test_content_analysis(self):
        """Test comprehensive content analysis"""
        analysis = self.analyzer.analyze_image_content(self.test_image)
        
        # Check that all expected keys are present
        expected_keys = ['dominant_colors', 'edge_density', 'contrast_regions', 
                        'color_segments', 'texture_analysis']
        for key in expected_keys:
            self.assertIn(key, analysis)
    
    def test_dominant_color_extraction(self):
        """Test dominant color extraction"""
        colors = self.analyzer._extract_dominant_colors(self.test_image)
        self.assertIsInstance(colors, list)
        if colors:
            self.assertIsInstance(colors[0], tuple)
            self.assertEqual(len(colors[0]), 2)  # (color, percentage)
    
    def test_color_segmentation(self):
        """Test color segmentation"""
        mask = self.analyzer._perform_color_segmentation(self.test_image)
        self.assertEqual(mask.shape, self.test_image.shape[:2])
        self.assertEqual(mask.dtype, np.uint8)


class TestIntelligentCropper(unittest.TestCase):
    """Test intelligent cropping functionality"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config = CroppingConfig()
        self.config.debug_mode = True
        self.cropper = IntelligentCropper(self.config)
        
        # Create test image file
        self.test_image_path = os.path.join(self.temp_dir, "test_bird.jpg")
        test_image = np.random.randint(0, 255, (600, 800, 3), dtype=np.uint8)
        # Add a bird-like shape
        cv2.ellipse(test_image, (400, 300), (80, 120), 0, 0, 360, (139, 69, 19), -1)
        cv2.circle(test_image, (400, 220), 50, (160, 82, 45), -1)
        cv2.imwrite(self.test_image_path, test_image)
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_crop_region_calculation(self):
        """Test crop region calculation"""
        image = cv2.imread(self.test_image_path)
        saliency_map = np.random.randint(0, 255, image.shape[:2], dtype=np.uint8)
        content_analysis = {'color_segments': np.zeros(image.shape[:2], dtype=np.uint8)}
        
        crop_region = self.cropper._calculate_optimal_crop_region(
            image, saliency_map, content_analysis
        )
        
        if crop_region:
            x, y, w, h = crop_region
            self.assertGreaterEqual(x, 0)
            self.assertGreaterEqual(y, 0)
            self.assertGreater(w, 0)
            self.assertGreater(h, 0)
    
    def test_target_dimensions_calculation(self):
        """Test target dimensions calculation for different aspect ratios"""
        width, height = 800, 600
        
        # Test different aspect ratios
        aspect_ratios = ["1:1", "4:3", "16:9", "original"]
        for ratio in aspect_ratios:
            self.config.output_aspect_ratio = ratio
            target_w, target_h = self.cropper._calculate_target_dimensions(width, height)
            self.assertGreater(target_w, 0)
            self.assertGreater(target_h, 0)
    
    def test_single_image_cropping(self):
        """Test single image cropping"""
        result = self.cropper.crop_image(self.test_image_path)
        # Result might be None if cropping fails, which is acceptable for synthetic images
        if result:
            self.assertTrue(os.path.exists(result))


class TestBatchProcessor(unittest.TestCase):
    """Test batch processing functionality"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.config = CroppingConfig()
        self.processor = AdvancedBatchProcessor(self.config, max_workers=2)
        
        # Create multiple test images
        for i in range(3):
            image_path = os.path.join(self.temp_dir, f"test_bird_{i}.jpg")
            test_image = np.random.randint(0, 255, (400, 600, 3), dtype=np.uint8)
            cv2.imwrite(image_path, test_image)
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_find_image_files(self):
        """Test image file discovery"""
        image_files = self.processor._find_image_files(self.temp_dir, recursive=False)
        self.assertEqual(len(image_files), 3)
        
        for file_path in image_files:
            self.assertTrue(file_path.exists())
            self.assertIn(file_path.suffix.lower(), ['.jpg', '.jpeg', '.png'])
    
    def test_batch_processing(self):
        """Test batch processing"""
        result = self.processor.process_directory(self.temp_dir)
        
        self.assertEqual(result.total_images, 3)
        self.assertGreaterEqual(result.successful_crops + result.failed_crops, result.total_images)
        self.assertIsInstance(result.processing_time, float)
        self.assertGreater(result.processing_time, 0)


class PerformanceBenchmark:
    """Performance benchmarking for the cropping bot"""
    
    def __init__(self):
        self.config = CroppingConfig()
        self.cropper = IntelligentCropper(self.config)
        self.temp_dir = tempfile.mkdtemp()
    
    def cleanup(self):
        shutil.rmtree(self.temp_dir)
    
    def benchmark_single_image_processing(self, image_sizes: List[tuple]) -> Dict:
        """Benchmark single image processing with different sizes"""
        results = {}
        
        for width, height in image_sizes:
            # Create test image
            test_image = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
            # Add bird-like features
            center = (width // 2, height // 2)
            cv2.ellipse(test_image, center, (width//10, height//8), 0, 0, 360, (139, 69, 19), -1)
            
            image_path = os.path.join(self.temp_dir, f"test_{width}x{height}.jpg")
            cv2.imwrite(image_path, test_image)
            
            # Benchmark processing time
            start_time = time.time()
            result = self.cropper.crop_image(image_path)
            processing_time = time.time() - start_time
            
            results[f"{width}x{height}"] = {
                'processing_time': processing_time,
                'success': result is not None,
                'file_size_mb': os.path.getsize(image_path) / (1024 * 1024)
            }
        
        return results
    
    def benchmark_saliency_methods(self) -> Dict:
        """Benchmark different saliency detection methods"""
        methods = ["spectral_residual", "fine_grained", "bing"]
        results = {}
        
        # Create test image
        test_image = np.random.randint(0, 255, (600, 800, 3), dtype=np.uint8)
        cv2.ellipse(test_image, (400, 300), (100, 150), 0, 0, 360, (139, 69, 19), -1)
        
        for method in methods:
            detector = SaliencyDetector(method)
            
            start_time = time.time()
            saliency_map = detector.compute_saliency_map(test_image)
            processing_time = time.time() - start_time
            
            results[method] = {
                'processing_time': processing_time,
                'success': saliency_map is not None
            }
        
        return results


def run_comprehensive_tests():
    """Run all tests and benchmarks"""
    print("="*60)
    print("BIRD CROPPING BOT - COMPREHENSIVE TEST SUITE")
    print("="*60)
    
    # Run unit tests
    print("\n1. Running Unit Tests...")
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestCroppingConfig,
        TestSaliencyDetector,
        TestContentAnalyzer,
        TestIntelligentCropper,
        TestBatchProcessor
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    test_result = runner.run(test_suite)
    
    # Performance benchmarks
    print("\n2. Running Performance Benchmarks...")
    benchmark = PerformanceBenchmark()
    
    try:
        # Benchmark different image sizes
        print("\nBenchmarking image sizes...")
        size_results = benchmark.benchmark_single_image_processing([
            (400, 300), (800, 600), (1200, 900), (1920, 1080)
        ])
        
        for size, result in size_results.items():
            status = "✓" if result['success'] else "✗"
            print(f"  {size}: {result['processing_time']:.3f}s {status}")
        
        # Benchmark saliency methods
        print("\nBenchmarking saliency methods...")
        saliency_results = benchmark.benchmark_saliency_methods()
        
        for method, result in saliency_results.items():
            status = "✓" if result['success'] else "✗"
            print(f"  {method}: {result['processing_time']:.3f}s {status}")
    
    finally:
        benchmark.cleanup()
    
    # Create sample images for manual testing
    print("\n3. Creating Sample Images...")
    generator = SampleImageGenerator("test_sample_images")
    generator.create_all_samples()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Unit Tests Run: {test_result.testsRun}")
    print(f"Failures: {len(test_result.failures)}")
    print(f"Errors: {len(test_result.errors)}")
    
    if test_result.failures:
        print("\nFailures:")
        for test, traceback in test_result.failures:
            print(f"  - {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if test_result.errors:
        print("\nErrors:")
        for test, traceback in test_result.errors:
            print(f"  - {test}: {traceback.split('Error:')[-1].strip()}")
    
    success_rate = ((test_result.testsRun - len(test_result.failures) - len(test_result.errors)) 
                   / test_result.testsRun * 100) if test_result.testsRun > 0 else 0
    print(f"\nOverall Success Rate: {success_rate:.1f}%")
    
    print(f"\nSample images created in: test_sample_images/")
    print("Run manual tests with: python bird_cropping_bot.py --input-dir test_sample_images --batch")
    print("="*60)


if __name__ == "__main__":
    run_comprehensive_tests()
