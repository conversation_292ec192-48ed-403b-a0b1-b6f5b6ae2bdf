#!/usr/bin/env python3
"""
Advanced Bird Image Cropping Bot
================================

A standalone computer vision tool for automatically cropping bird images
using advanced saliency detection, object detection, and intelligent
cropping algorithms.

Author: AI Assistant
Version: 1.0.0
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance, ImageFilter
import os
import argparse
import json
import time
from pathlib import Path
import logging
from typing import Tuple, List, Dict, Optional, Union
from dataclasses import dataclass
from sklearn.cluster import KMeans
import matplotlib.pyplot as plt

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('bird_cropping.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class CroppingConfig:
    """Configuration class for cropping parameters"""
    saliency_method: str = "spectral_residual"  # spectral_residual, fine_grained, bing
    output_aspect_ratio: str = "16:9"  # 1:1, 4:3, 16:9, original
    min_crop_size: Tuple[int, int] = (200, 200)
    max_crop_size: Tuple[int, int] = (2048, 2048)
    padding_ratio: float = 0.1
    quality_threshold: float = 0.3
    enable_face_detection: bool = True
    enable_color_segmentation: bool = True
    enable_edge_enhancement: bool = True
    output_format: str = "jpg"  # jpg, png
    output_quality: int = 95
    batch_size: int = 10
    debug_mode: bool = False

class SaliencyDetector:
    """Advanced saliency detection using multiple algorithms"""
    
    def __init__(self, method: str = "spectral_residual"):
        self.method = method
        self._init_detectors()
    
    def _init_detectors(self):
        """Initialize OpenCV saliency detectors"""
        try:
            if self.method == "spectral_residual":
                self.detector = cv2.saliency.StaticSaliencySpectralResidual_create()
            elif self.method == "fine_grained":
                self.detector = cv2.saliency.StaticSaliencyFineGrained_create()
            elif self.method == "bing":
                self.detector = cv2.saliency.ObjectnessBING_create()
            else:
                logger.warning(f"Unknown saliency method: {self.method}, using spectral_residual")
                self.detector = cv2.saliency.StaticSaliencySpectralResidual_create()
        except Exception as e:
            logger.error(f"Failed to initialize saliency detector: {e}")
            self.detector = None
    
    def compute_saliency_map(self, image: np.ndarray) -> Optional[np.ndarray]:
        """
        Compute saliency map for the input image
        
        Args:
            image: Input image as numpy array (BGR format)
            
        Returns:
            Saliency map as numpy array or None if failed
        """
        if self.detector is None:
            return None
            
        try:
            success, saliency_map = self.detector.computeSaliency(image)
            if success:
                # Normalize to 0-255 range
                saliency_map = cv2.normalize(saliency_map, None, 0, 255, cv2.NORM_MINMAX)
                return saliency_map.astype(np.uint8)
            else:
                logger.warning("Saliency computation failed")
                return None
        except Exception as e:
            logger.error(f"Error computing saliency map: {e}")
            return None

class ContentAnalyzer:
    """Advanced content analysis for bird image processing"""
    
    def __init__(self, config: CroppingConfig):
        self.config = config
        self._init_detectors()
    
    def _init_detectors(self):
        """Initialize detection models"""
        try:
            # Initialize face cascade for bird face detection
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_alt.xml'
            if os.path.exists(cascade_path):
                self.face_cascade = cv2.CascadeClassifier(cascade_path)
            else:
                self.face_cascade = None
                logger.warning("Face cascade not found, face detection disabled")
        except Exception as e:
            logger.error(f"Failed to initialize detectors: {e}")
            self.face_cascade = None
    
    def analyze_image_content(self, image: np.ndarray) -> Dict:
        """
        Comprehensive content analysis of the image
        
        Args:
            image: Input image as numpy array (BGR format)
            
        Returns:
            Dictionary containing analysis results
        """
        analysis = {
            'dominant_colors': self._extract_dominant_colors(image),
            'edge_density': self._compute_edge_density(image),
            'contrast_regions': self._find_high_contrast_regions(image),
            'color_segments': self._perform_color_segmentation(image),
            'texture_analysis': self._analyze_texture(image)
        }
        
        if self.config.enable_face_detection and self.face_cascade is not None:
            analysis['face_regions'] = self._detect_faces(image)
        
        return analysis
    
    def _extract_dominant_colors(self, image: np.ndarray, n_colors: int = 5) -> List[Tuple]:
        """Extract dominant colors using K-means clustering"""
        try:
            # Convert BGR to RGB
            rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            pixels = rgb_image.reshape(-1, 3)
            
            # Sample pixels for faster processing
            if len(pixels) > 10000:
                indices = np.random.choice(len(pixels), 10000, replace=False)
                pixels = pixels[indices]
            
            kmeans = KMeans(n_clusters=n_colors, random_state=42, n_init=10)
            kmeans.fit(pixels)
            
            colors = kmeans.cluster_centers_.astype(int)
            labels = kmeans.labels_
            
            # Calculate color frequencies
            unique, counts = np.unique(labels, return_counts=True)
            color_percentages = counts / len(labels)
            
            # Sort by frequency
            sorted_indices = np.argsort(color_percentages)[::-1]
            dominant_colors = [(tuple(colors[i]), color_percentages[i]) for i in sorted_indices]
            
            return dominant_colors
        except Exception as e:
            logger.error(f"Error extracting dominant colors: {e}")
            return []
    
    def _compute_edge_density(self, image: np.ndarray) -> np.ndarray:
        """Compute edge density map using Canny edge detection"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            edges = cv2.Canny(gray, 50, 150)
            
            # Compute edge density using sliding window
            kernel = np.ones((15, 15), np.float32) / (15 * 15)
            edge_density = cv2.filter2D(edges.astype(np.float32), -1, kernel)
            
            return edge_density
        except Exception as e:
            logger.error(f"Error computing edge density: {e}")
            return np.zeros(image.shape[:2], dtype=np.float32)
    
    def _find_high_contrast_regions(self, image: np.ndarray) -> List:
        """Find regions with high contrast"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Compute local contrast using Laplacian
            laplacian = cv2.Laplacian(gray, cv2.CV_64F)
            contrast_map = np.abs(laplacian)
            
            # Threshold to find high contrast areas
            _, high_contrast = cv2.threshold(
                contrast_map.astype(np.uint8), 0, 255, 
                cv2.THRESH_BINARY + cv2.THRESH_OTSU
            )
            
            # Find contours
            contours, _ = cv2.findContours(
                high_contrast, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE
            )
            
            # Filter by area
            min_area = image.shape[0] * image.shape[1] * 0.01
            significant_contours = [c for c in contours if cv2.contourArea(c) > min_area]
            
            return significant_contours
        except Exception as e:
            logger.error(f"Error finding high contrast regions: {e}")
            return []
    
    def _perform_color_segmentation(self, image: np.ndarray) -> np.ndarray:
        """Perform color-based segmentation to separate bird from background"""
        try:
            # Convert to HSV for better color analysis
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Create masks for different color ranges
            masks = []
            
            # Brown/earth tones (common in birds)
            brown_lower = np.array([10, 50, 50])
            brown_upper = np.array([25, 255, 255])
            masks.append(cv2.inRange(hsv, brown_lower, brown_upper))
            
            # Gray tones
            gray_lower = np.array([0, 0, 50])
            gray_upper = np.array([180, 50, 200])
            masks.append(cv2.inRange(hsv, gray_lower, gray_upper))
            
            # Dark colors
            dark_lower = np.array([0, 0, 0])
            dark_upper = np.array([180, 255, 80])
            masks.append(cv2.inRange(hsv, dark_lower, dark_upper))
            
            # Combine masks
            combined_mask = masks[0]
            for mask in masks[1:]:
                combined_mask = cv2.bitwise_or(combined_mask, mask)
            
            # Clean up mask with morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            cleaned_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            cleaned_mask = cv2.morphologyEx(cleaned_mask, cv2.MORPH_OPEN, kernel)
            
            return cleaned_mask
        except Exception as e:
            logger.error(f"Error in color segmentation: {e}")
            return np.zeros(image.shape[:2], dtype=np.uint8)
    
    def _analyze_texture(self, image: np.ndarray) -> np.ndarray:
        """Analyze texture patterns using local variance"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Compute local variance as texture measure
            kernel_size = 9
            kernel = np.ones((kernel_size, kernel_size), np.float32) / (kernel_size * kernel_size)
            
            local_mean = cv2.filter2D(gray.astype(np.float32), -1, kernel)
            local_variance = cv2.filter2D((gray.astype(np.float32) - local_mean)**2, -1, kernel)
            
            return local_variance
        except Exception as e:
            logger.error(f"Error analyzing texture: {e}")
            return np.zeros(image.shape[:2], dtype=np.float32)
    
    def _detect_faces(self, image: np.ndarray) -> List:
        """Detect bird faces/heads using cascade classifier"""
        try:
            if self.face_cascade is None:
                return []
            
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            faces = self.face_cascade.detectMultiScale(
                gray, scaleFactor=1.1, minNeighbors=5, minSize=(30, 30)
            )
            
            return faces.tolist() if len(faces) > 0 else []
        except Exception as e:
            logger.error(f"Error detecting faces: {e}")
            return []

class IntelligentCropper:
    """Main cropping engine with intelligent algorithms"""
    
    def __init__(self, config: CroppingConfig):
        self.config = config
        self.saliency_detector = SaliencyDetector(config.saliency_method)
        self.content_analyzer = ContentAnalyzer(config)
    
    def crop_image(self, image_path: Union[str, Path]) -> Optional[str]:
        """
        Crop a single image using intelligent algorithms
        
        Args:
            image_path: Path to input image
            
        Returns:
            Path to cropped image or None if failed
        """
        try:
            # Load image
            image = cv2.imread(str(image_path))
            if image is None:
                logger.error(f"Failed to load image: {image_path}")
                return None
            
            logger.info(f"Processing image: {image_path}")
            logger.info(f"Original size: {image.shape[1]}x{image.shape[0]}")
            
            # Perform content analysis
            content_analysis = self.content_analyzer.analyze_image_content(image)
            
            # Compute saliency map
            saliency_map = self.saliency_detector.compute_saliency_map(image)
            
            # Determine optimal crop region
            crop_region = self._calculate_optimal_crop_region(
                image, saliency_map, content_analysis
            )
            
            if crop_region is None:
                logger.warning(f"Could not determine crop region for {image_path}")
                return None
            
            # Apply crop
            cropped_image = self._apply_crop(image, crop_region)
            
            # Enhance cropped image
            if self.config.enable_edge_enhancement:
                cropped_image = self._enhance_image(cropped_image)
            
            # Save result
            output_path = self._generate_output_path(image_path)
            success = self._save_image(cropped_image, output_path)
            
            if success:
                logger.info(f"Successfully cropped: {output_path}")
                return str(output_path)
            else:
                logger.error(f"Failed to save cropped image: {output_path}")
                return None
                
        except Exception as e:
            logger.error(f"Error cropping image {image_path}: {e}")
            return None

    def _calculate_optimal_crop_region(self, image: np.ndarray, saliency_map: Optional[np.ndarray],
                                     content_analysis: Dict) -> Optional[Tuple[int, int, int, int]]:
        """
        Calculate optimal crop region using multiple strategies

        Args:
            image: Input image
            saliency_map: Saliency map (can be None)
            content_analysis: Content analysis results

        Returns:
            Crop region as (x, y, width, height) or None
        """
        try:
            height, width = image.shape[:2]

            # Strategy 1: Saliency-based cropping
            if saliency_map is not None:
                saliency_region = self._get_saliency_based_region(saliency_map)
                if saliency_region:
                    logger.info("Using saliency-based cropping")
                    return self._refine_crop_region(saliency_region, width, height)

            # Strategy 2: Color segmentation-based cropping
            if 'color_segments' in content_analysis:
                segment_region = self._get_segmentation_based_region(content_analysis['color_segments'])
                if segment_region:
                    logger.info("Using color segmentation-based cropping")
                    return self._refine_crop_region(segment_region, width, height)

            # Strategy 3: High contrast region-based cropping
            if 'contrast_regions' in content_analysis and content_analysis['contrast_regions']:
                contrast_region = self._get_contrast_based_region(content_analysis['contrast_regions'])
                if contrast_region:
                    logger.info("Using contrast-based cropping")
                    return self._refine_crop_region(contrast_region, width, height)

            # Strategy 4: Face detection-based cropping
            if 'face_regions' in content_analysis and content_analysis['face_regions']:
                face_region = self._get_face_based_region(content_analysis['face_regions'])
                if face_region:
                    logger.info("Using face detection-based cropping")
                    return self._refine_crop_region(face_region, width, height)

            # Strategy 5: Fallback to center crop with intelligent bias
            logger.info("Using intelligent center crop as fallback")
            return self._get_intelligent_center_crop(width, height)

        except Exception as e:
            logger.error(f"Error calculating crop region: {e}")
            return None

    def _get_saliency_based_region(self, saliency_map: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """Extract crop region based on saliency map"""
        try:
            # Threshold saliency map to get high saliency regions
            threshold = np.percentile(saliency_map, 80)  # Top 20% saliency
            binary_saliency = (saliency_map > threshold).astype(np.uint8) * 255

            # Find contours of high saliency regions
            contours, _ = cv2.findContours(binary_saliency, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return None

            # Find the largest contour
            largest_contour = max(contours, key=cv2.contourArea)

            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)

            return (x, y, w, h)

        except Exception as e:
            logger.error(f"Error in saliency-based region detection: {e}")
            return None

    def _get_segmentation_based_region(self, color_mask: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """Extract crop region based on color segmentation"""
        try:
            # Find contours in the color mask
            contours, _ = cv2.findContours(color_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return None

            # Filter contours by area
            min_area = color_mask.shape[0] * color_mask.shape[1] * 0.02  # At least 2% of image
            valid_contours = [c for c in contours if cv2.contourArea(c) > min_area]

            if not valid_contours:
                return None

            # Find the largest valid contour
            largest_contour = max(valid_contours, key=cv2.contourArea)

            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)

            return (x, y, w, h)

        except Exception as e:
            logger.error(f"Error in segmentation-based region detection: {e}")
            return None

    def _get_contrast_based_region(self, contrast_contours: List) -> Optional[Tuple[int, int, int, int]]:
        """Extract crop region based on high contrast areas"""
        try:
            if not contrast_contours:
                return None

            # Find the largest contrast region
            largest_contour = max(contrast_contours, key=cv2.contourArea)

            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)

            return (x, y, w, h)

        except Exception as e:
            logger.error(f"Error in contrast-based region detection: {e}")
            return None

    def _get_face_based_region(self, face_regions: List) -> Optional[Tuple[int, int, int, int]]:
        """Extract crop region based on detected faces"""
        try:
            if not face_regions:
                return None

            # Use the first (largest) detected face
            x, y, w, h = face_regions[0]

            # Expand region to include more of the bird's body
            expansion_factor = 2.5
            center_x, center_y = x + w // 2, y + h // 2

            new_w = int(w * expansion_factor)
            new_h = int(h * expansion_factor)
            new_x = center_x - new_w // 2
            new_y = center_y - new_h // 2

            return (new_x, new_y, new_w, new_h)

        except Exception as e:
            logger.error(f"Error in face-based region detection: {e}")
            return None

    def _get_intelligent_center_crop(self, width: int, height: int) -> Tuple[int, int, int, int]:
        """Get intelligent center crop with bias towards upper portion"""
        try:
            # Calculate target dimensions based on aspect ratio
            target_width, target_height = self._calculate_target_dimensions(width, height)

            # Center horizontally, bias towards upper portion vertically
            x = (width - target_width) // 2
            y = int((height - target_height) * 0.3)  # 30% from top

            # Ensure bounds
            x = max(0, min(x, width - target_width))
            y = max(0, min(y, height - target_height))

            return (x, y, target_width, target_height)

        except Exception as e:
            logger.error(f"Error in intelligent center crop: {e}")
            # Fallback to simple center crop
            target_width = int(width * 0.8)
            target_height = int(height * 0.8)
            x = (width - target_width) // 2
            y = (height - target_height) // 2
            return (x, y, target_width, target_height)

    def _calculate_target_dimensions(self, width: int, height: int) -> Tuple[int, int]:
        """Calculate target dimensions based on aspect ratio setting"""
        try:
            if self.config.output_aspect_ratio == "1:1":
                # Square crop
                size = min(width, height)
                return (size, size)
            elif self.config.output_aspect_ratio == "4:3":
                # 4:3 aspect ratio
                if width / height > 4/3:
                    # Width is limiting factor
                    target_height = int(width * 3 / 4)
                    return (width, min(target_height, height))
                else:
                    # Height is limiting factor
                    target_width = int(height * 4 / 3)
                    return (min(target_width, width), height)
            elif self.config.output_aspect_ratio == "16:9":
                # 16:9 aspect ratio
                if width / height > 16/9:
                    # Width is limiting factor
                    target_height = int(width * 9 / 16)
                    return (width, min(target_height, height))
                else:
                    # Height is limiting factor
                    target_width = int(height * 16 / 9)
                    return (min(target_width, width), height)
            else:
                # Original aspect ratio with 80% size
                return (int(width * 0.8), int(height * 0.8))

        except Exception as e:
            logger.error(f"Error calculating target dimensions: {e}")
            return (int(width * 0.8), int(height * 0.8))

    def _refine_crop_region(self, region: Tuple[int, int, int, int],
                           image_width: int, image_height: int) -> Tuple[int, int, int, int]:
        """Refine crop region to ensure it meets constraints"""
        try:
            x, y, w, h = region

            # Add padding
            padding_x = int(w * self.config.padding_ratio)
            padding_y = int(h * self.config.padding_ratio)

            x = max(0, x - padding_x)
            y = max(0, y - padding_y)
            w = min(image_width - x, w + 2 * padding_x)
            h = min(image_height - y, h + 2 * padding_y)

            # Ensure minimum size
            min_w, min_h = self.config.min_crop_size
            if w < min_w or h < min_h:
                # Expand to minimum size
                center_x, center_y = x + w // 2, y + h // 2
                w = max(w, min_w)
                h = max(h, min_h)
                x = max(0, center_x - w // 2)
                y = max(0, center_y - h // 2)

                # Adjust if exceeds image bounds
                if x + w > image_width:
                    x = image_width - w
                if y + h > image_height:
                    y = image_height - h

            # Ensure maximum size
            max_w, max_h = self.config.max_crop_size
            if w > max_w or h > max_h:
                # Scale down while maintaining aspect ratio
                scale = min(max_w / w, max_h / h)
                w = int(w * scale)
                h = int(h * scale)

                # Re-center
                center_x, center_y = x + w // 2, y + h // 2
                x = max(0, center_x - w // 2)
                y = max(0, center_y - h // 2)

            return (x, y, w, h)

        except Exception as e:
            logger.error(f"Error refining crop region: {e}")
            return region

    def _apply_crop(self, image: np.ndarray, crop_region: Tuple[int, int, int, int]) -> np.ndarray:
        """Apply crop to image"""
        try:
            x, y, w, h = crop_region
            cropped = image[y:y+h, x:x+w]
            logger.info(f"Cropped to size: {cropped.shape[1]}x{cropped.shape[0]}")
            return cropped
        except Exception as e:
            logger.error(f"Error applying crop: {e}")
            return image

    def _enhance_image(self, image: np.ndarray) -> np.ndarray:
        """Enhance cropped image quality"""
        try:
            # Convert to PIL for enhancement
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))

            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(1.2)

            # Enhance contrast slightly
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.1)

            # Convert back to OpenCV format
            enhanced = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return enhanced

        except Exception as e:
            logger.error(f"Error enhancing image: {e}")
            return image

    def _generate_output_path(self, input_path: Union[str, Path]) -> Path:
        """Generate output path for cropped image"""
        input_path = Path(input_path)
        output_dir = input_path.parent / "cropped"
        output_dir.mkdir(exist_ok=True)

        # Generate filename with timestamp
        timestamp = int(time.time())
        stem = input_path.stem
        extension = self.config.output_format

        output_filename = f"{stem}_cropped_{timestamp}.{extension}"
        return output_dir / output_filename

    def _save_image(self, image: np.ndarray, output_path: Path) -> bool:
        """Save cropped image"""
        try:
            if self.config.output_format.lower() == "jpg":
                cv2.imwrite(
                    str(output_path), image,
                    [cv2.IMWRITE_JPEG_QUALITY, self.config.output_quality]
                )
            else:
                cv2.imwrite(str(output_path), image)

            return True
        except Exception as e:
            logger.error(f"Error saving image: {e}")
            return False

    def crop_batch(self, input_dir: Union[str, Path],
                   file_patterns: List[str] = None) -> List[str]:
        """
        Crop multiple images in batch

        Args:
            input_dir: Directory containing input images
            file_patterns: List of file patterns to match (e.g., ['*.jpg', '*.png'])

        Returns:
            List of paths to successfully cropped images
        """
        if file_patterns is None:
            file_patterns = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff']

        input_dir = Path(input_dir)
        if not input_dir.exists():
            logger.error(f"Input directory does not exist: {input_dir}")
            return []

        # Find all matching files
        image_files = []
        for pattern in file_patterns:
            image_files.extend(input_dir.glob(pattern))
            image_files.extend(input_dir.glob(pattern.upper()))

        if not image_files:
            logger.warning(f"No image files found in {input_dir}")
            return []

        logger.info(f"Found {len(image_files)} images to process")

        # Process images in batches
        successful_crops = []
        batch_size = self.config.batch_size

        for i in range(0, len(image_files), batch_size):
            batch = image_files[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1}/{(len(image_files) + batch_size - 1)//batch_size}")

            for image_file in batch:
                try:
                    result = self.crop_image(image_file)
                    if result:
                        successful_crops.append(result)
                except Exception as e:
                    logger.error(f"Error processing {image_file}: {e}")
                    continue

        logger.info(f"Successfully cropped {len(successful_crops)}/{len(image_files)} images")
        return successful_crops


def load_config(config_path: str = "cropping_config.json") -> CroppingConfig:
    """Load configuration from JSON file"""
    try:
        if os.path.exists(config_path):
            with open(config_path, 'r') as f:
                config_dict = json.load(f)

            # Create config object with loaded values
            config = CroppingConfig()
            for key, value in config_dict.items():
                if hasattr(config, key):
                    setattr(config, key, value)

            logger.info(f"Loaded configuration from {config_path}")
            return config
        else:
            logger.info("Using default configuration")
            return CroppingConfig()
    except Exception as e:
        logger.error(f"Error loading configuration: {e}")
        return CroppingConfig()


def save_default_config(config_path: str = "cropping_config.json"):
    """Save default configuration to JSON file"""
    try:
        config = CroppingConfig()
        config_dict = {
            'saliency_method': config.saliency_method,
            'output_aspect_ratio': config.output_aspect_ratio,
            'min_crop_size': config.min_crop_size,
            'max_crop_size': config.max_crop_size,
            'padding_ratio': config.padding_ratio,
            'quality_threshold': config.quality_threshold,
            'enable_face_detection': config.enable_face_detection,
            'enable_color_segmentation': config.enable_color_segmentation,
            'enable_edge_enhancement': config.enable_edge_enhancement,
            'output_format': config.output_format,
            'output_quality': config.output_quality,
            'batch_size': config.batch_size,
            'debug_mode': config.debug_mode
        }

        with open(config_path, 'w') as f:
            json.dump(config_dict, f, indent=2)

        logger.info(f"Saved default configuration to {config_path}")
    except Exception as e:
        logger.error(f"Error saving configuration: {e}")


def create_sample_images():
    """Create sample bird images for testing (placeholder function)"""
    sample_dir = Path("sample_images")
    sample_dir.mkdir(exist_ok=True)

    logger.info("Sample images directory created at: sample_images/")
    logger.info("Please add your bird images to this directory for testing")


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Advanced Bird Image Cropping Bot",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Crop a single image
  python bird_cropping_bot.py --input bird.jpg

  # Crop all images in a directory
  python bird_cropping_bot.py --input-dir ./screenshots --batch

  # Use specific saliency method and aspect ratio
  python bird_cropping_bot.py --input bird.jpg --saliency spectral_residual --aspect-ratio 1:1

  # Generate default configuration file
  python bird_cropping_bot.py --create-config

  # Create sample images directory
  python bird_cropping_bot.py --create-samples
        """
    )

    parser.add_argument('--input', '-i', type=str, help='Input image file path')
    parser.add_argument('--input-dir', '-d', type=str, help='Input directory containing images')
    parser.add_argument('--batch', '-b', action='store_true', help='Process all images in input directory')
    parser.add_argument('--config', '-c', type=str, default='cropping_config.json',
                       help='Configuration file path')
    parser.add_argument('--saliency', '-s', type=str,
                       choices=['spectral_residual', 'fine_grained', 'bing'],
                       help='Saliency detection method')
    parser.add_argument('--aspect-ratio', '-a', type=str,
                       choices=['1:1', '4:3', '16:9', 'original'],
                       help='Output aspect ratio')
    parser.add_argument('--output-format', '-f', type=str, choices=['jpg', 'png'],
                       help='Output image format')
    parser.add_argument('--quality', '-q', type=int, help='Output quality (1-100 for JPEG)')
    parser.add_argument('--debug', action='store_true', help='Enable debug mode')
    parser.add_argument('--create-config', action='store_true',
                       help='Create default configuration file')
    parser.add_argument('--create-samples', action='store_true',
                       help='Create sample images directory')

    args = parser.parse_args()

    # Handle special commands
    if args.create_config:
        save_default_config(args.config)
        return

    if args.create_samples:
        create_sample_images()
        return

    # Load configuration
    config = load_config(args.config)

    # Override config with command line arguments
    if args.saliency:
        config.saliency_method = args.saliency
    if args.aspect_ratio:
        config.output_aspect_ratio = args.aspect_ratio
    if args.output_format:
        config.output_format = args.output_format
    if args.quality:
        config.output_quality = args.quality
    if args.debug:
        config.debug_mode = True
        logging.getLogger().setLevel(logging.DEBUG)

    # Create cropper instance
    cropper = IntelligentCropper(config)

    # Process images
    if args.input:
        # Single image processing
        if not os.path.exists(args.input):
            logger.error(f"Input file does not exist: {args.input}")
            return

        result = cropper.crop_image(args.input)
        if result:
            print(f"Successfully cropped image: {result}")
        else:
            print("Failed to crop image")

    elif args.input_dir or args.batch:
        # Batch processing
        input_dir = args.input_dir or "."

        if not os.path.exists(input_dir):
            logger.error(f"Input directory does not exist: {input_dir}")
            return

        results = cropper.crop_batch(input_dir)
        print(f"Successfully cropped {len(results)} images")

        if results:
            print("Cropped images:")
            for result in results:
                print(f"  - {result}")

    else:
        parser.print_help()


if __name__ == "__main__":
    main()
