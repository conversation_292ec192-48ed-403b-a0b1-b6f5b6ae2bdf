#!/usr/bin/env python3
"""
Script untuk memperbaiki masalah cropping burung yang masih menyisakan bagian hitam/background.
Fokus pada analisis warna burung dan deteksi boundary yang lebih akurat.
"""

import os
import sys
import json
from PIL import Image, ImageDraw, ImageEnhance
import cv2
import numpy as np
from collections import Counter

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class BirdCropFixer:
    """Class untuk memperbaiki masalah cropping burung"""
    
    def __init__(self):
        self.debug_mode = True
        
    def analyze_bird_image(self, image_path):
        """Analisis mendalam image burung untuk cropping yang lebih baik"""
        try:
            print(f"🔍 Analyzing bird image: {image_path}")
            
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                print(f"📊 Original size: {img.width}x{img.height}")
                
                # Convert to numpy array for analysis
                img_array = np.array(img)
                
                # Analyze colors
                colors = self._analyze_colors(img_array)
                
                # Detect background vs bird
                bird_mask = self._create_bird_mask(img_array, colors)
                
                # Find optimal crop region
                crop_region = self._find_optimal_crop_region(img_array, bird_mask)
                
                return {
                    'original_size': (img.width, img.height),
                    'colors': colors,
                    'crop_region': crop_region,
                    'bird_mask': bird_mask
                }
                
        except Exception as e:
            print(f"❌ Error analyzing image: {e}")
            return None
    
    def _analyze_colors(self, img_array):
        """Analisis warna untuk membedakan burung dari background"""
        try:
            # Get image dimensions
            h, w = img_array.shape[:2]
            
            # Sample pixels from different regions
            center_region = img_array[h//4:3*h//4, w//4:3*w//4]  # Center area (likely bird)
            edge_region = np.concatenate([
                img_array[:h//8, :].reshape(-1, 3),      # Top edge
                img_array[-h//8:, :].reshape(-1, 3),     # Bottom edge
                img_array[:, :w//8].reshape(-1, 3),      # Left edge
                img_array[:, -w//8:].reshape(-1, 3)      # Right edge
            ])
            
            # Analyze center colors (bird colors)
            center_pixels = center_region.reshape(-1, 3)
            center_colors = self._get_dominant_colors(center_pixels, n_colors=5)
            
            # Analyze edge colors (background colors)
            edge_colors = self._get_dominant_colors(edge_region, n_colors=3)
            
            print(f"🎨 Bird colors (center): {[tuple(c) for c in center_colors[:3]]}")
            print(f"🎨 Background colors (edges): {[tuple(c) for c in edge_colors[:2]]}")
            
            return {
                'bird_colors': center_colors,
                'background_colors': edge_colors,
                'center_region': center_region,
                'edge_region': edge_region
            }
            
        except Exception as e:
            print(f"❌ Error analyzing colors: {e}")
            return None
    
    def _get_dominant_colors(self, pixels, n_colors=5):
        """Get dominant colors from pixel array"""
        try:
            # Remove very dark and very bright pixels
            mask = np.all(pixels > 10, axis=1) & np.all(pixels < 250, axis=1)
            filtered_pixels = pixels[mask]
            
            if len(filtered_pixels) < 10:
                return pixels[:n_colors] if len(pixels) >= n_colors else pixels
            
            # Simple clustering by grouping similar colors
            unique_colors = []
            tolerance = 25
            
            for pixel in filtered_pixels[::max(1, len(filtered_pixels)//1000)]:  # Sample pixels
                found_similar = False
                for existing_color in unique_colors:
                    if np.linalg.norm(pixel - existing_color) < tolerance:
                        found_similar = True
                        break
                
                if not found_similar:
                    unique_colors.append(pixel)
                
                if len(unique_colors) >= n_colors * 2:  # Get more than needed
                    break
            
            # Sort by frequency (approximate)
            color_counts = []
            for color in unique_colors:
                count = np.sum(np.linalg.norm(filtered_pixels - color, axis=1) < tolerance)
                color_counts.append((color, count))
            
            # Sort by count and return top colors
            color_counts.sort(key=lambda x: x[1], reverse=True)
            return [color for color, count in color_counts[:n_colors]]
            
        except Exception as e:
            print(f"❌ Error getting dominant colors: {e}")
            return pixels[:n_colors] if len(pixels) >= n_colors else pixels
    
    def _create_bird_mask(self, img_array, colors):
        """Create mask to separate bird from background"""
        try:
            if not colors:
                return None
            
            h, w = img_array.shape[:2]
            mask = np.zeros((h, w), dtype=np.uint8)
            
            bird_colors = colors['bird_colors']
            background_colors = colors['background_colors']
            
            # Convert to HSV for better color separation
            hsv = cv2.cvtColor(img_array, cv2.COLOR_RGB2HSV)
            
            # Create masks for bird colors
            bird_masks = []
            for bird_color in bird_colors[:3]:  # Top 3 bird colors
                # Ensure bird_color is proper numpy array
                bird_color_array = np.array(bird_color, dtype=np.uint8).reshape(1, 1, 3)

                # Convert to HSV
                bird_hsv = cv2.cvtColor(bird_color_array, cv2.COLOR_RGB2HSV)[0][0]

                # Create range around this color with proper data types
                tolerance_h = 15
                tolerance_sv = 40

                lower = np.array([
                    max(0, int(bird_hsv[0]) - tolerance_h),
                    max(0, int(bird_hsv[1]) - tolerance_sv),
                    max(0, int(bird_hsv[2]) - tolerance_sv)
                ], dtype=np.uint8)

                upper = np.array([
                    min(179, int(bird_hsv[0]) + tolerance_h),
                    min(255, int(bird_hsv[1]) + tolerance_sv),
                    min(255, int(bird_hsv[2]) + tolerance_sv)
                ], dtype=np.uint8)

                bird_mask = cv2.inRange(hsv, lower, upper)
                bird_masks.append(bird_mask)
            
            # Combine bird masks
            if bird_masks:
                combined_bird_mask = bird_masks[0]
                for mask in bird_masks[1:]:
                    combined_bird_mask = cv2.bitwise_or(combined_bird_mask, mask)
            else:
                combined_bird_mask = np.zeros((h, w), dtype=np.uint8)
            
            # Remove background colors from bird mask
            for bg_color in background_colors[:2]:  # Top 2 background colors
                # Ensure proper data type
                bg_color_array = np.array(bg_color, dtype=np.uint8).reshape(1, 1, 3)
                bg_hsv = cv2.cvtColor(bg_color_array, cv2.COLOR_RGB2HSV)[0][0]

                lower = np.array([
                    max(0, int(bg_hsv[0]) - 10),
                    max(0, int(bg_hsv[1]) - 30),
                    max(0, int(bg_hsv[2]) - 30)
                ], dtype=np.uint8)

                upper = np.array([
                    min(179, int(bg_hsv[0]) + 10),
                    min(255, int(bg_hsv[1]) + 30),
                    min(255, int(bg_hsv[2]) + 30)
                ], dtype=np.uint8)

                bg_mask = cv2.inRange(hsv, lower, upper)
                combined_bird_mask = cv2.bitwise_and(combined_bird_mask, cv2.bitwise_not(bg_mask))
            
            # Clean up mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            cleaned_mask = cv2.morphologyEx(combined_bird_mask, cv2.MORPH_CLOSE, kernel)
            cleaned_mask = cv2.morphologyEx(cleaned_mask, cv2.MORPH_OPEN, kernel)
            
            # Fill holes
            kernel_fill = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 7))
            cleaned_mask = cv2.morphologyEx(cleaned_mask, cv2.MORPH_CLOSE, kernel_fill)
            
            print(f"🎭 Created bird mask with {np.sum(cleaned_mask > 0)} bird pixels")
            
            return cleaned_mask
            
        except Exception as e:
            print(f"❌ Error creating bird mask: {e}")
            return None
    
    def _find_optimal_crop_region(self, img_array, bird_mask):
        """Find optimal crop region based on bird mask"""
        try:
            if bird_mask is None:
                return None
            
            h, w = img_array.shape[:2]
            
            # Find contours in bird mask
            contours, _ = cv2.findContours(bird_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                print("⚠️ No bird contours found")
                return None
            
            # Get the largest contour (main bird)
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Get bounding rectangle
            x, y, cw, ch = cv2.boundingRect(largest_contour)
            
            print(f"📦 Bird bounding box: {cw}x{ch} at ({x}, {y})")
            
            # Calculate intelligent padding
            # More padding for smaller birds, less for larger birds
            bird_area_ratio = (cw * ch) / (w * h)
            
            if bird_area_ratio < 0.1:  # Small bird
                padding_factor = 0.3
            elif bird_area_ratio < 0.3:  # Medium bird
                padding_factor = 0.2
            else:  # Large bird
                padding_factor = 0.15
            
            padding_x = int(cw * padding_factor)
            padding_y = int(ch * padding_factor)
            
            # Apply padding with bounds checking
            crop_x = max(0, x - padding_x)
            crop_y = max(0, y - padding_y)
            crop_w = min(w - crop_x, cw + 2 * padding_x)
            crop_h = min(h - crop_y, ch + 2 * padding_y)
            
            # Ensure minimum size
            min_size = min(w, h) * 0.2
            if crop_w < min_size or crop_h < min_size:
                print("⚠️ Crop too small, expanding...")
                center_x, center_y = x + cw//2, y + ch//2
                crop_size = max(min_size, max(crop_w, crop_h))
                
                crop_x = max(0, center_x - crop_size//2)
                crop_y = max(0, center_y - crop_size//2)
                crop_w = min(w - crop_x, crop_size)
                crop_h = min(h - crop_y, crop_size)
            
            crop_region = {
                'x': int(crop_x),
                'y': int(crop_y),
                'width': int(crop_w),
                'height': int(crop_h),
                'padding_x': padding_x,
                'padding_y': padding_y,
                'bird_area_ratio': bird_area_ratio
            }
            
            print(f"✂️ Optimal crop region: {crop_w}x{crop_h} at ({crop_x}, {crop_y})")
            
            return crop_region
            
        except Exception as e:
            print(f"❌ Error finding crop region: {e}")
            return None
    
    def fix_bird_crop(self, image_path, output_path=None):
        """Main method to fix bird cropping"""
        try:
            print(f"🔧 Fixing bird crop for: {image_path}")
            
            # Analyze image
            analysis = self.analyze_bird_image(image_path)
            if not analysis:
                print("❌ Failed to analyze image")
                return None
            
            # Load original image
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                crop_region = analysis['crop_region']
                if not crop_region:
                    print("❌ No valid crop region found")
                    return None
                
                # Perform crop
                cropped = img.crop((
                    crop_region['x'],
                    crop_region['y'],
                    crop_region['x'] + crop_region['width'],
                    crop_region['y'] + crop_region['height']
                ))
                
                # Enhance the cropped image
                enhanced = self._enhance_cropped_bird(cropped)
                
                # Save result
                if output_path is None:
                    base_name = os.path.splitext(image_path)[0]
                    output_path = f"{base_name}_fixed_crop.png"
                
                enhanced.save(output_path)
                
                print(f"✅ Fixed crop saved: {output_path}")
                print(f"📊 Original: {img.width}x{img.height} → Cropped: {enhanced.width}x{enhanced.height}")
                
                # Save debug info if enabled
                if self.debug_mode:
                    self._save_debug_info(image_path, analysis, output_path)
                
                return enhanced
                
        except Exception as e:
            print(f"❌ Error fixing bird crop: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _enhance_cropped_bird(self, cropped_img):
        """Enhance the cropped bird image"""
        try:
            # Slight contrast enhancement
            enhancer = ImageEnhance.Contrast(cropped_img)
            enhanced = enhancer.enhance(1.1)
            
            # Slight sharpness enhancement
            sharpness_enhancer = ImageEnhance.Sharpness(enhanced)
            enhanced = sharpness_enhancer.enhance(1.05)
            
            return enhanced
            
        except Exception as e:
            print(f"⚠️ Error enhancing image: {e}")
            return cropped_img
    
    def _save_debug_info(self, original_path, analysis, output_path):
        """Save debug information"""
        try:
            debug_dir = f"debug_bird_fix_{os.path.basename(original_path).split('.')[0]}"
            os.makedirs(debug_dir, exist_ok=True)
            
            # Save bird mask
            if analysis['bird_mask'] is not None:
                mask_path = os.path.join(debug_dir, "bird_mask.png")
                cv2.imwrite(mask_path, analysis['bird_mask'])
            
            # Save analysis info
            info_path = os.path.join(debug_dir, "analysis_info.json")
            with open(info_path, 'w') as f:
                json.dump({
                    'original_path': original_path,
                    'output_path': output_path,
                    'original_size': analysis['original_size'],
                    'crop_region': analysis['crop_region'],
                    'bird_colors': [[int(x) for x in color] for color in analysis['colors']['bird_colors'][:3]],
                    'background_colors': [[int(x) for x in color] for color in analysis['colors']['background_colors'][:2]]
                }, f, indent=2)
            
            print(f"🐛 Debug info saved to: {debug_dir}")
            
        except Exception as e:
            print(f"⚠️ Error saving debug info: {e}")

def main():
    """Main function to test the bird crop fixer"""
    print("🔧 BIRD CROP FIXER")
    print("Fixes cropping issues that leave black/background areas")
    print("=" * 50)
    
    # Look for bird images in current directory
    image_extensions = ['.png', '.jpg', '.jpeg', '.bmp']
    bird_images = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in image_extensions):
            # Include test images but exclude debug/result images
            if 'debug' not in file.lower() and 'result' not in file.lower() and 'cropped' not in file.lower():
                bird_images.append(file)
    
    if bird_images:
        print(f"📸 Found {len(bird_images)} images to process")
        
        fixer = BirdCropFixer()
        
        for image_path in bird_images[:3]:  # Process first 3 images
            print(f"\n{'='*60}")
            print(f"🔧 PROCESSING: {image_path}")
            print(f"{'='*60}")
            
            result = fixer.fix_bird_crop(image_path)
            
            if result:
                print(f"✅ Successfully fixed crop for {image_path}")
            else:
                print(f"❌ Failed to fix crop for {image_path}")
    else:
        print("📷 No bird images found in current directory")
        print("💡 Place bird images in this directory to test the crop fixer")

if __name__ == "__main__":
    main()
