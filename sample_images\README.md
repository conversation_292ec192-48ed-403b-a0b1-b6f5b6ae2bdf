# Sample Bird Images for Testing

This directory contains synthetic bird images created for testing the cropping bot functionality.

## Test Images:

1. **centered_bird.jpg** - Bird positioned in center of frame (easy case)
2. **off_center_bird.jpg** - Bird positioned off-center (moderate case)
3. **multiple_birds.jpg** - Multiple birds with one main subject (challenging)
4. **complex_background_bird.jpg** - Bird with complex background (challenging)
5. **bird_silhouette.jpg** - Bird silhouette against bright background (edge case)
6. **bird_close_up.jpg** - Close-up bird head (cropping precision test)
7. **bird_in_flight.jpg** - Bird with spread wings (shape variation)
8. **small_bird_large_background.jpg** - Small bird in large frame (detection challenge)

## Usage:

Test the cropping bot with these images:

```bash
# Test single image
python bird_cropping_bot.py --input sample_images/centered_bird.jpg

# Test all sample images
python bird_cropping_bot.py --input-dir sample_images --batch

# Test with different settings
python bird_cropping_bot.py --input sample_images/complex_background_bird.jpg --saliency fine_grained
```

## Expected Results:

- **Easy cases**: Should crop accurately with default settings
- **Moderate cases**: Should work well with appropriate saliency method
- **Challenging cases**: May require fine-tuned configuration
- **Edge cases**: Test robustness of fallback strategies

Add your own bird images to this directory for additional testing.
