#!/usr/bin/env python3
"""
Test script khusus untuk memperbaiki cropping pada file 00_original.png
"""

import os
import sys
from PIL import Image

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fix_bird_cropping import Bird<PERSON>ropFix<PERSON>

def test_00_original():
    """Test fix cropping pada 00_original.png"""
    print("🎯 TESTING FIX BIRD CROPPING ON 00_ORIGINAL.PNG")
    print("=" * 60)
    
    image_path = "00_original.png"
    
    # Check if file exists
    if not os.path.exists(image_path):
        print(f"❌ File not found: {image_path}")
        return False
    
    # Show original image info
    try:
        with Image.open(image_path) as img:
            print(f"📸 Original image: {image_path}")
            print(f"📊 Size: {img.width}x{img.height}")
            print(f"📊 Mode: {img.mode}")
            print(f"📊 Format: {img.format}")
    except Exception as e:
        print(f"❌ Error reading image: {e}")
        return False
    
    # Initialize fixer
    fixer = BirdCropFixer()
    fixer.debug_mode = True  # Enable debug mode for detailed analysis
    
    # Fix the cropping
    print(f"\n🔧 Starting bird crop fix...")
    result = fixer.fix_bird_crop(image_path, "00_original_FIXED_CROP.png")
    
    if result:
        print(f"\n✅ SUCCESS! Fixed crop saved as: 00_original_FIXED_CROP.png")
        print(f"📊 Result size: {result.width}x{result.height}")
        
        # Show improvement
        with Image.open(image_path) as original:
            reduction_ratio = (result.width * result.height) / (original.width * original.height)
            print(f"📈 Size reduction: {(1-reduction_ratio)*100:.1f}%")
            print(f"📈 Focus improvement: Cropped to {reduction_ratio*100:.1f}% of original")
        
        return True
    else:
        print(f"\n❌ FAILED to fix crop for {image_path}")
        return False

def compare_with_existing_result():
    """Compare with existing 03_final_result.png if available"""
    print(f"\n🔍 COMPARING WITH EXISTING RESULT")
    print("=" * 40)
    
    existing_result = "03_final_result.png"
    new_result = "00_original_FIXED_CROP.png"
    
    if os.path.exists(existing_result) and os.path.exists(new_result):
        try:
            with Image.open(existing_result) as existing, Image.open(new_result) as new:
                print(f"📊 Existing result: {existing.width}x{existing.height}")
                print(f"📊 New fixed crop: {new.width}x{new.height}")
                
                # Calculate which one is more focused (smaller usually means better crop)
                existing_area = existing.width * existing.height
                new_area = new.width * new.height
                
                if new_area < existing_area:
                    improvement = ((existing_area - new_area) / existing_area) * 100
                    print(f"🎉 New crop is {improvement:.1f}% more focused!")
                elif new_area > existing_area:
                    difference = ((new_area - existing_area) / existing_area) * 100
                    print(f"⚠️ New crop is {difference:.1f}% larger (less focused)")
                else:
                    print(f"📊 Both crops have similar size")
                    
        except Exception as e:
            print(f"❌ Error comparing results: {e}")
    else:
        if not os.path.exists(existing_result):
            print(f"⚠️ Existing result not found: {existing_result}")
        if not os.path.exists(new_result):
            print(f"⚠️ New result not found: {new_result}")

def analyze_debug_info():
    """Analyze debug information generated"""
    print(f"\n🐛 ANALYZING DEBUG INFORMATION")
    print("=" * 35)
    
    debug_dir = "debug_bird_fix_00_original"
    
    if os.path.exists(debug_dir):
        print(f"📁 Debug directory found: {debug_dir}")
        
        # List debug files
        debug_files = os.listdir(debug_dir)
        print(f"📄 Debug files generated:")
        for file in debug_files:
            print(f"   • {file}")
        
        # Read analysis info if available
        analysis_file = os.path.join(debug_dir, "analysis_info.json")
        if os.path.exists(analysis_file):
            try:
                import json
                with open(analysis_file, 'r') as f:
                    analysis = json.load(f)
                
                print(f"\n📊 ANALYSIS SUMMARY:")
                print(f"   Original size: {analysis.get('original_size', 'N/A')}")
                print(f"   Crop region: {analysis.get('crop_region', 'N/A')}")
                print(f"   Bird colors detected: {len(analysis.get('bird_colors', []))}")
                print(f"   Background colors: {len(analysis.get('background_colors', []))}")
                
                if 'bird_colors' in analysis:
                    print(f"   Top bird colors:")
                    for i, color in enumerate(analysis['bird_colors'][:3]):
                        print(f"     {i+1}. RGB{color}")
                        
            except Exception as e:
                print(f"⚠️ Error reading analysis: {e}")
    else:
        print(f"⚠️ Debug directory not found: {debug_dir}")

def main():
    """Main test function"""
    print("🎯 COMPREHENSIVE TEST: 00_ORIGINAL.PNG CROP FIX")
    print("This will test the advanced bird cropping fix on the specific image")
    print("=" * 70)
    
    # Test 1: Fix the cropping
    success = test_00_original()
    
    # Test 2: Compare with existing result
    compare_with_existing_result()
    
    # Test 3: Analyze debug information
    analyze_debug_info()
    
    # Final summary
    print(f"\n🏆 FINAL SUMMARY")
    print("=" * 20)
    
    if success:
        print("✅ Crop fix completed successfully!")
        print("📁 Files generated:")
        print("   • 00_original_FIXED_CROP.png - The improved crop")
        print("   • debug_bird_fix_00_original/ - Debug information")
        print("\n💡 The new crop should have:")
        print("   • Removed black/background areas")
        print("   • Better focus on the bird")
        print("   • Intelligent padding around the bird")
        print("   • Enhanced contrast and sharpness")
    else:
        print("❌ Crop fix failed")
        print("💡 Check the error messages above for troubleshooting")
    
    print(f"\n🔍 Next steps:")
    print("   1. Compare 00_original_FIXED_CROP.png with the original")
    print("   2. Check debug files for detailed analysis")
    print("   3. If satisfied, use this method in your scraper")

if __name__ == "__main__":
    main()
