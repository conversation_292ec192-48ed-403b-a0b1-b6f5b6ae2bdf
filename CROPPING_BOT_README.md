# Advanced Bird Image Cropping Bot

A sophisticated computer vision tool for automatically cropping bird images using advanced saliency detection, object detection, and intelligent cropping algorithms. This standalone bot processes screenshots captured by eBird scraper bots and applies state-of-the-art image processing techniques to extract high-quality bird images.

## Features

### 🔍 Advanced Computer Vision
- **Saliency Detection**: Multiple algorithms (Spectral Residual, Fine Grained, BING)
- **Content Analysis**: Object detection, edge detection, color analysis
- **Face Detection**: Specialized bird face/eye detection
- **Color Segmentation**: HSV-based background separation
- **Texture Analysis**: Local variance-based texture detection

### 🎯 Intelligent Cropping
- **Multi-Strategy Approach**: Combines multiple detection methods
- **Adaptive Algorithms**: Automatically selects best cropping strategy
- **Aspect Ratio Support**: 1:1, 4:3, 16:9, or original ratios
- **Composition Rules**: Rule of thirds, centered subjects
- **Quality Preservation**: Maintains original image quality

### ⚡ Batch Processing
- **Parallel Processing**: Multi-threaded batch operations
- **Progress Tracking**: Real-time progress updates
- **Comprehensive Reporting**: Detailed processing statistics
- **Error Handling**: Robust error recovery and logging

## Installation

### Prerequisites
```bash
# Python 3.8 or higher required
python --version

# Install required packages
pip install opencv-python
pip install opencv-contrib-python
pip install pillow
pip install numpy
pip install scikit-learn
pip install matplotlib
```

### Quick Setup
```bash
# Install dependencies
pip install -r requirements.txt

# Create default configuration
python bird_cropping_bot.py --create-config

# Create sample images directory
python bird_cropping_bot.py --create-samples
```

## Usage

### Single Image Processing
```bash
# Basic cropping
python bird_cropping_bot.py --input bird_screenshot.jpg

# With specific settings
python bird_cropping_bot.py --input bird.jpg --saliency spectral_residual --aspect-ratio 1:1

# High quality output
python bird_cropping_bot.py --input bird.jpg --output-format png --quality 100
```

### Batch Processing
```bash
# Process all images in directory
python bird_cropping_bot.py --input-dir ./screenshots --batch

# Advanced batch processing
python batch_crop.py --input-dir ./images --recursive --workers 8

# With detailed reporting
python batch_crop.py --input-dir ./images --report detailed_report.json
```

### Configuration Options
```bash
# Use custom configuration
python bird_cropping_bot.py --input bird.jpg --config my_config.json

# Debug mode
python bird_cropping_bot.py --input bird.jpg --debug
```

## Configuration

### Configuration File (cropping_config.json)
```json
{
  "saliency_method": "spectral_residual",
  "output_aspect_ratio": "16:9",
  "min_crop_size": [200, 200],
  "max_crop_size": [2048, 2048],
  "padding_ratio": 0.1,
  "quality_threshold": 0.3,
  "enable_face_detection": true,
  "enable_color_segmentation": true,
  "enable_edge_enhancement": true,
  "output_format": "jpg",
  "output_quality": 95,
  "batch_size": 10,
  "debug_mode": false
}
```

### Configuration Parameters

| Parameter | Description | Options |
|-----------|-------------|---------|
| `saliency_method` | Saliency detection algorithm | `spectral_residual`, `fine_grained`, `bing` |
| `output_aspect_ratio` | Output image aspect ratio | `1:1`, `4:3`, `16:9`, `original` |
| `min_crop_size` | Minimum crop dimensions | `[width, height]` |
| `max_crop_size` | Maximum crop dimensions | `[width, height]` |
| `padding_ratio` | Padding around detected region | `0.0` to `1.0` |
| `enable_face_detection` | Enable bird face detection | `true`/`false` |
| `enable_color_segmentation` | Enable color-based segmentation | `true`/`false` |
| `enable_edge_enhancement` | Enhance cropped images | `true`/`false` |
| `output_format` | Output image format | `jpg`, `png` |
| `output_quality` | JPEG quality (1-100) | Integer |
| `batch_size` | Images per batch | Integer |

## Advanced Features

### Saliency Detection Methods

#### Spectral Residual (Default)
- Fast and efficient
- Good for general bird detection
- Works well with varied backgrounds

#### Fine Grained
- More detailed analysis
- Better for complex scenes
- Slower processing

#### BING (Objectness)
- Object-focused detection
- Good for clear bird subjects
- Requires specific training

### Cropping Strategies

The bot uses a multi-strategy approach, trying methods in order:

1. **Saliency-based**: Uses saliency maps to find attention-grabbing regions
2. **Color segmentation**: Separates bird colors from background
3. **High contrast**: Finds regions with significant contrast changes
4. **Face detection**: Locates bird faces/heads and expands region
5. **Intelligent center**: Fallback with upper-bias positioning

### Quality Enhancement

- **Sharpness Enhancement**: Improves image clarity
- **Contrast Adjustment**: Optimizes contrast levels
- **Edge Preservation**: Maintains important edge details
- **Noise Reduction**: Reduces processing artifacts

## File Structure

```
bird-cropping-bot/
├── bird_cropping_bot.py      # Main cropping engine
├── batch_crop.py             # Advanced batch processor
├── cropping_config.json      # Configuration file
├── requirements.txt          # Python dependencies
├── CROPPING_BOT_README.md    # This documentation
├── sample_images/            # Test images directory
├── cropped/                  # Output directory
└── logs/                     # Log files
    ├── bird_cropping.log
    └── batch_cropping.log
```

## Integration with eBird Scraper

### Workflow Integration
1. **eBird Scraper** captures screenshots → `screenshots/` directory
2. **Cropping Bot** processes screenshots → `cropped/` directory
3. **Quality Review** → Final bird image collection

### Automated Pipeline
```bash
# Example automation script
#!/bin/bash

# Run eBird scraper
python ebird_scraper.py --species "Northern Cardinal" --limit 50

# Process captured screenshots
python batch_crop.py --input-dir ./screenshots --recursive

# Generate report
python batch_crop.py --input-dir ./screenshots --report processing_report.json

echo "Pipeline completed successfully!"
```

## Performance Optimization

### Hardware Recommendations
- **CPU**: Multi-core processor for parallel processing
- **RAM**: 8GB+ for large batch processing
- **Storage**: SSD for faster I/O operations

### Optimization Tips
```python
# Adjust batch size based on available memory
"batch_size": 20  # Increase for more RAM

# Use appropriate number of workers
python batch_crop.py --workers 8  # Match CPU cores

# Optimize image formats
"output_format": "jpg"  # Smaller files
"output_quality": 85    # Balance quality/size
```

## Troubleshooting

### Common Issues

#### OpenCV Installation Problems
```bash
# Uninstall and reinstall OpenCV
pip uninstall opencv-python opencv-contrib-python
pip install opencv-python==********
pip install opencv-contrib-python==********
```

#### Memory Issues with Large Batches
```json
{
  "batch_size": 5,
  "max_crop_size": [1024, 1024]
}
```

#### Poor Cropping Results
```json
{
  "saliency_method": "fine_grained",
  "enable_color_segmentation": true,
  "padding_ratio": 0.15
}
```

### Debug Mode
```bash
# Enable detailed logging
python bird_cropping_bot.py --input bird.jpg --debug

# Check log files
tail -f bird_cropping.log
```

## API Reference

### IntelligentCropper Class
```python
from bird_cropping_bot import IntelligentCropper, CroppingConfig

# Create configuration
config = CroppingConfig()
config.saliency_method = "spectral_residual"
config.output_aspect_ratio = "16:9"

# Initialize cropper
cropper = IntelligentCropper(config)

# Crop single image
result = cropper.crop_image("bird.jpg")

# Batch processing
results = cropper.crop_batch("./images")
```

### Configuration Management
```python
from bird_cropping_bot import load_config, save_default_config

# Load configuration
config = load_config("my_config.json")

# Save default configuration
save_default_config("default_config.json")
```

## Examples and Testing

### Sample Usage Examples
```bash
# Process eBird screenshots
python bird_cropping_bot.py --input-dir ./ebird_screenshots --batch --aspect-ratio 16:9

# High-quality processing for publication
python bird_cropping_bot.py --input rare_bird.jpg --output-format png --quality 100 --saliency fine_grained

# Batch process with custom settings
python batch_crop.py --input-dir ./bird_photos --recursive --workers 4 --config high_quality_config.json
```

### Testing with Sample Images
1. Create sample images directory: `python bird_cropping_bot.py --create-samples`
2. Add your test bird images to `sample_images/`
3. Run test processing: `python bird_cropping_bot.py --input-dir sample_images --batch`
4. Check results in `sample_images/cropped/`

## Version History

- **v1.0.0**: Initial release with advanced computer vision features
- **v1.1.0**: Added batch processing and parallel execution
- **v1.2.0**: Enhanced saliency detection and quality improvements
