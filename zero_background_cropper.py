#!/usr/bin/env python3
"""
Zero Background Cropper - Complete Background Removal
====================================================

This script completely removes all black/dark background pixels and crops
to the absolute minimum bounding box containing only the bird pixels.

Author: AI Assistant
Version: 1.0.0
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
import argparse
import logging
from pathlib import Path
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ZeroBackgroundCropper:
    """Complete background removal and zero-padding cropper"""
    
    def __init__(self, black_threshold: int = 30, noise_threshold: int = 100):
        self.black_threshold = black_threshold  # Threshold for detecting black pixels
        self.noise_threshold = noise_threshold  # Minimum area to keep (removes noise)
    
    def remove_background_completely(self, image_path: str, output_path: str = None) -> str:
        """
        Remove all background and crop to absolute minimum containing only bird
        
        Args:
            image_path: Path to input image
            output_path: Path for output image (optional)
            
        Returns:
            Path to processed image
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Failed to load image: {image_path}")
            
            logger.info(f"Processing: {image_path}")
            logger.info(f"Original size: {image.shape[1]}x{image.shape[0]}")
            
            # Step 1: Create precise bird mask
            bird_mask = self._create_precise_bird_mask(image)
            
            # Step 2: Refine mask to remove noise and get clean bird outline
            refined_mask = self._refine_bird_mask(bird_mask)
            
            # Step 3: Find absolute minimum bounding box
            min_bbox = self._find_absolute_minimum_bbox(refined_mask)
            
            if min_bbox is None:
                raise ValueError("Could not detect bird in image")
            
            # Step 4: Apply zero-background crop
            cropped_bird = self._apply_zero_background_crop(image, refined_mask, min_bbox)
            
            # Step 5: Enhance the final result
            enhanced_bird = self._enhance_final_result(cropped_bird)
            
            # Generate output path if not provided
            if output_path is None:
                input_path = Path(image_path)
                timestamp = int(time.time())
                output_path = input_path.parent / f"{input_path.stem}_ZERO_BACKGROUND_{timestamp}.jpg"
            
            # Save with maximum quality
            cv2.imwrite(str(output_path), enhanced_bird, [cv2.IMWRITE_JPEG_QUALITY, 100])
            
            logger.info(f"Zero background image saved: {output_path}")
            logger.info(f"Final size: {enhanced_bird.shape[1]}x{enhanced_bird.shape[0]}")
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error in zero background cropping: {e}")
            raise
    
    def _create_precise_bird_mask(self, image: np.ndarray) -> np.ndarray:
        """Create precise mask identifying only bird pixels"""
        try:
            # Convert to different color spaces for analysis
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Method 1: Black background detection
            black_mask = self._detect_black_background(image, gray)
            
            # Method 2: Color-based bird detection
            color_mask = self._detect_bird_by_color(hsv)
            
            # Method 3: Texture-based detection
            texture_mask = self._detect_bird_by_texture(gray)
            
            # Method 4: Edge-based detection
            edge_mask = self._detect_bird_by_edges(gray)
            
            # Combine masks intelligently
            combined_mask = self._combine_bird_masks([color_mask, texture_mask, edge_mask])
            
            # Remove black background areas
            final_mask = cv2.bitwise_and(combined_mask, cv2.bitwise_not(black_mask))
            
            return final_mask
            
        except Exception as e:
            logger.error(f"Error creating bird mask: {e}")
            raise
    
    def _detect_black_background(self, image: np.ndarray, gray: np.ndarray) -> np.ndarray:
        """Detect black/dark background pixels"""
        try:
            # Create mask for very dark pixels
            _, black_mask = cv2.threshold(gray, self.black_threshold, 255, cv2.THRESH_BINARY_INV)
            
            # Also check for near-black colors in BGR
            b, g, r = cv2.split(image)
            
            # Pixels where all channels are below threshold
            dark_b = b < self.black_threshold
            dark_g = g < self.black_threshold
            dark_r = r < self.black_threshold
            
            # Combine all dark channel conditions
            all_dark = np.logical_and(np.logical_and(dark_b, dark_g), dark_r).astype(np.uint8) * 255
            
            # Combine grayscale and color-based black detection
            combined_black = cv2.bitwise_or(black_mask, all_dark)
            
            # Clean up the mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            combined_black = cv2.morphologyEx(combined_black, cv2.MORPH_CLOSE, kernel)
            
            logger.info(f"Detected {np.sum(combined_black > 0)} black background pixels")
            
            return combined_black
            
        except Exception as e:
            logger.error(f"Error detecting black background: {e}")
            return np.zeros_like(gray)
    
    def _detect_bird_by_color(self, hsv: np.ndarray) -> np.ndarray:
        """Detect bird using color analysis"""
        try:
            # Comprehensive bird color ranges
            bird_ranges = [
                # Brown/tan birds
                ([5, 20, 30], [30, 255, 255]),
                # Gray birds  
                ([0, 0, 40], [180, 50, 200]),
                # Dark birds (but not black background)
                ([0, 0, 35], [180, 255, 120]),
                # Colorful birds (red, orange, yellow)
                ([0, 50, 50], [35, 255, 255]),
                # Blue birds
                ([90, 30, 30], [130, 255, 255]),
                # Green birds
                ([35, 30, 30], [85, 255, 255]),
                # White/light birds
                ([0, 0, 150], [180, 50, 255])
            ]
            
            # Create combined color mask
            color_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
            
            for lower, upper in bird_ranges:
                lower = np.array(lower)
                upper = np.array(upper)
                mask = cv2.inRange(hsv, lower, upper)
                color_mask = cv2.bitwise_or(color_mask, mask)
            
            # Clean up color mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            color_mask = cv2.morphologyEx(color_mask, cv2.MORPH_OPEN, kernel)
            color_mask = cv2.morphologyEx(color_mask, cv2.MORPH_CLOSE, kernel)
            
            return color_mask
            
        except Exception as e:
            logger.error(f"Error in color-based detection: {e}")
            return np.zeros(hsv.shape[:2], dtype=np.uint8)
    
    def _detect_bird_by_texture(self, gray: np.ndarray) -> np.ndarray:
        """Detect bird using texture analysis"""
        try:
            # Calculate local variance (texture measure)
            kernel_size = 5
            kernel = np.ones((kernel_size, kernel_size), np.float32) / (kernel_size * kernel_size)
            
            # Convert to float for calculations
            gray_float = gray.astype(np.float32)
            
            # Calculate local mean and variance
            local_mean = cv2.filter2D(gray_float, -1, kernel)
            local_variance = cv2.filter2D((gray_float - local_mean)**2, -1, kernel)
            
            # Normalize variance
            variance_norm = cv2.normalize(local_variance, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
            
            # Threshold to get textured regions
            _, texture_mask = cv2.threshold(variance_norm, 20, 255, cv2.THRESH_BINARY)
            
            return texture_mask
            
        except Exception as e:
            logger.error(f"Error in texture-based detection: {e}")
            return np.zeros_like(gray)
    
    def _detect_bird_by_edges(self, gray: np.ndarray) -> np.ndarray:
        """Detect bird using edge analysis"""
        try:
            # Apply bilateral filter to preserve edges while reducing noise
            filtered = cv2.bilateralFilter(gray, 9, 75, 75)
            
            # Multi-scale edge detection
            edges1 = cv2.Canny(filtered, 20, 60)
            edges2 = cv2.Canny(filtered, 40, 100)
            edges3 = cv2.Canny(filtered, 60, 150)
            
            # Combine edges
            combined_edges = cv2.bitwise_or(edges1, edges2)
            combined_edges = cv2.bitwise_or(combined_edges, edges3)
            
            # Dilate edges to create regions
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            edge_regions = cv2.dilate(combined_edges, kernel, iterations=2)
            
            return edge_regions
            
        except Exception as e:
            logger.error(f"Error in edge-based detection: {e}")
            return np.zeros_like(gray)
    
    def _combine_bird_masks(self, masks: list) -> np.ndarray:
        """Combine multiple bird detection masks"""
        try:
            valid_masks = [mask for mask in masks if mask is not None]
            if not valid_masks:
                raise ValueError("No valid masks to combine")
            
            # Start with first mask
            combined = valid_masks[0].copy()
            
            # Combine using OR operation (union of all detections)
            for mask in valid_masks[1:]:
                combined = cv2.bitwise_or(combined, mask)
            
            return combined
            
        except Exception as e:
            logger.error(f"Error combining masks: {e}")
            raise
    
    def _refine_bird_mask(self, mask: np.ndarray) -> np.ndarray:
        """Refine bird mask to remove noise and get clean outline"""
        try:
            # Remove small noise regions
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            # Filter contours by area
            valid_contours = [c for c in contours if cv2.contourArea(c) > self.noise_threshold]
            
            if not valid_contours:
                logger.warning("No valid contours found, using original mask")
                return mask
            
            # Find the largest contour (main bird)
            largest_contour = max(valid_contours, key=cv2.contourArea)
            
            # Create refined mask with only the largest contour
            refined_mask = np.zeros_like(mask)
            cv2.fillPoly(refined_mask, [largest_contour], 255)
            
            # Apply slight morphological closing to fill small gaps
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            refined_mask = cv2.morphologyEx(refined_mask, cv2.MORPH_CLOSE, kernel)
            
            logger.info(f"Refined mask contains {np.sum(refined_mask > 0)} bird pixels")
            
            return refined_mask
            
        except Exception as e:
            logger.error(f"Error refining mask: {e}")
            return mask
    
    def _find_absolute_minimum_bbox(self, mask: np.ndarray) -> tuple:
        """Find absolute minimum bounding box containing all bird pixels"""
        try:
            # Find all non-zero points
            points = cv2.findNonZero(mask)
            if points is None:
                return None
            
            # Get bounding rectangle with zero padding
            x, y, w, h = cv2.boundingRect(points)
            
            logger.info(f"Absolute minimum bounding box: x={x}, y={y}, w={w}, h={h}")
            
            return (x, y, w, h)
            
        except Exception as e:
            logger.error(f"Error finding minimum bbox: {e}")
            return None
    
    def _apply_zero_background_crop(self, image: np.ndarray, mask: np.ndarray, bbox: tuple) -> np.ndarray:
        """Apply crop with zero background padding"""
        try:
            x, y, w, h = bbox
            
            # Crop both image and mask to bounding box
            cropped_image = image[y:y+h, x:x+w]
            cropped_mask = mask[y:y+h, x:x+w]
            
            # Apply mask to remove any remaining background pixels
            # Convert mask to 3-channel
            mask_3ch = cv2.cvtColor(cropped_mask, cv2.COLOR_GRAY2BGR)
            
            # Apply mask (keep only bird pixels)
            result = cv2.bitwise_and(cropped_image, mask_3ch)
            
            # Optional: Set background to white instead of black for better visibility
            # Uncomment the next lines if you want white background instead of black
            # background = cv2.bitwise_not(mask_3ch)
            # white_bg = np.ones_like(cropped_image) * 255
            # white_background = cv2.bitwise_and(white_bg, background)
            # result = cv2.add(result, white_background)
            
            return result
            
        except Exception as e:
            logger.error(f"Error applying zero background crop: {e}")
            return image
    
    def _enhance_final_result(self, image: np.ndarray) -> np.ndarray:
        """Enhance the final cropped bird image"""
        try:
            # Convert to PIL for enhancement
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(1.4)
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.2)
            
            # Enhance color saturation slightly
            enhancer = ImageEnhance.Color(pil_image)
            pil_image = enhancer.enhance(1.1)
            
            # Convert back to OpenCV format
            enhanced = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return enhanced
            
        except Exception as e:
            logger.error(f"Error enhancing final result: {e}")
            return image


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Zero Background Cropper - Complete Background Removal",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Remove all background and crop to minimum
  python zero_background_cropper.py --input 00_original_PERFECT_CROP_1px.jpg
  
  # Custom black threshold
  python zero_background_cropper.py --input image.jpg --black-threshold 40
  
  # Custom output path
  python zero_background_cropper.py --input image.jpg --output bird_only.jpg
        """
    )
    
    parser.add_argument('--input', '-i', type=str, required=True, help='Input image path')
    parser.add_argument('--output', '-o', type=str, help='Output image path (optional)')
    parser.add_argument('--black-threshold', type=int, default=30, 
                       help='Threshold for detecting black pixels (default: 30)')
    parser.add_argument('--noise-threshold', type=int, default=100,
                       help='Minimum area to keep (removes noise, default: 100)')
    
    args = parser.parse_args()
    
    # Create zero background cropper instance
    cropper = ZeroBackgroundCropper(
        black_threshold=args.black_threshold,
        noise_threshold=args.noise_threshold
    )
    
    # Process the image
    try:
        result = cropper.remove_background_completely(args.input, args.output)
        print(f"✅ Successfully created zero background image: {result}")
    except Exception as e:
        print(f"❌ Failed to remove background: {e}")


if __name__ == "__main__":
    main()
