#!/usr/bin/env python3
"""
Test script untuk menguji implementasi enhanced color tracking di ScraperBot
untuk UUID f697e07b-5251-416c-85fa-5b99967decc4
"""

import os
import sys
from PIL import Image, ImageDraw
import cv2
import numpy as np

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_bird_images():
    """Create various test bird images for testing"""
    test_images = []
    
    # Test Image 1: Brown bird
    img1 = Image.new('RGB', (800, 600), (245, 245, 245))  # Light background
    draw1 = ImageDraw.Draw(img1)
    
    # Brown bird body
    draw1.ellipse([300, 250, 500, 350], fill=(139, 69, 19))  # Brown body
    draw1.ellipse([250, 200, 350, 280], fill=(101, 67, 33))  # Darker head
    draw1.ellipse([320, 270, 380, 310], fill=(105, 105, 105))  # Gray wing
    
    img1.save("test_brown_bird.png")
    test_images.append("test_brown_bird.png")
    
    # Test Image 2: Gray bird
    img2 = Image.new('RGB', (800, 600), (250, 250, 250))
    draw2 = ImageDraw.Draw(img2)
    
    # Gray bird
    draw2.ellipse([350, 200, 450, 300], fill=(128, 128, 128))  # Gray body
    draw2.ellipse([320, 180, 380, 240], fill=(105, 105, 105))  # Darker head
    draw2.ellipse([360, 220, 400, 260], fill=(169, 169, 169))  # Light wing
    
    img2.save("test_gray_bird.png")
    test_images.append("test_gray_bird.png")
    
    # Test Image 3: Colorful bird
    img3 = Image.new('RGB', (800, 600), (240, 248, 255))
    draw3 = ImageDraw.Draw(img3)
    
    # Colorful bird
    draw3.ellipse([300, 220, 500, 380], fill=(220, 20, 60))   # Red body
    draw3.ellipse([250, 180, 350, 260], fill=(255, 140, 0))  # Orange head
    draw3.ellipse([320, 250, 420, 320], fill=(50, 205, 50))  # Green wing
    draw3.ellipse([360, 280, 400, 320], fill=(30, 144, 255)) # Blue patch
    
    img3.save("test_colorful_bird.png")
    test_images.append("test_colorful_bird.png")
    
    return test_images

def test_individual_methods():
    """Test individual cropping methods without full ScraperBot initialization"""
    print("🧪 Testing Individual Enhanced Methods")
    print("=" * 50)
    
    # Create test images
    test_images = create_test_bird_images()
    
    # We'll test the methods by importing them directly
    try:
        # Import the methods we need to test
        from scraperBot import ScraperBot
        
        # Create a minimal bot instance for testing methods
        class TestBot:
            def __init__(self):
                # Copy the methods we want to test
                bot = ScraperBot.__new__(ScraperBot)  # Create without __init__
                
                # Copy the methods
                self._extract_dominant_colors = bot._extract_dominant_colors.__get__(self, TestBot)
                self._identify_bird_type_from_colors = bot._identify_bird_type_from_colors.__get__(self, TestBot)
                self._create_intelligent_color_mask = bot._create_intelligent_color_mask.__get__(self, TestBot)
                self._crop_using_intelligent_mask = bot._crop_using_intelligent_mask.__get__(self, TestBot)
                self._intelligent_color_tracking_crop = bot._intelligent_color_tracking_crop.__get__(self, TestBot)
                self._score_contour_for_bird_type = bot._score_contour_for_bird_type.__get__(self, TestBot)
                self._calculate_intelligent_padding = bot._calculate_intelligent_padding.__get__(self, TestBot)
                self._rgb_to_hsv_opencv = bot._rgb_to_hsv_opencv.__get__(self, TestBot)
                self._extract_colors_basic = bot._extract_colors_basic.__get__(self, TestBot)
        
        test_bot = TestBot()
        
        for i, test_image in enumerate(test_images):
            print(f"\n🖼️ Testing on {test_image}")
            print("-" * 30)
            
            try:
                with Image.open(test_image) as img:
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    
                    # Convert to OpenCV format
                    img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
                    
                    # Test the intelligent color tracking method
                    result = test_bot._intelligent_color_tracking_crop(img_cv, img)
                    
                    if result:
                        print(f"✅ Success! Cropped to {result.width}x{result.height}")
                        
                        # Save result
                        result_path = f"result_{i+1}_{test_image}"
                        result.save(result_path)
                        print(f"💾 Saved result: {result_path}")
                    else:
                        print("❌ Method failed")
                        
            except Exception as e:
                print(f"❌ Error testing {test_image}: {e}")
                import traceback
                traceback.print_exc()
    
    except Exception as e:
        print(f"❌ Error setting up test: {e}")
        import traceback
        traceback.print_exc()

def test_with_debug_uuid():
    """Test specifically for the problematic UUID"""
    print(f"\n🎯 Testing for UUID: f697e07b-5251-416c-85fa-5b99967decc4")
    print("=" * 60)
    
    # Create debug directory
    debug_dir = "debug_enhanced_f697e07b"
    os.makedirs(debug_dir, exist_ok=True)
    
    # Test with our created images
    test_images = create_test_bird_images()
    
    for i, test_image in enumerate(test_images):
        print(f"\n📸 Processing {test_image} for UUID analysis...")
        
        try:
            # Copy image to debug directory
            with Image.open(test_image) as img:
                debug_original = os.path.join(debug_dir, f"uuid_test_{i+1}_original.png")
                img.save(debug_original)
                
                # Test our enhanced method
                from scraperBot import ScraperBot
                
                # Create minimal test instance
                class UUIDTestBot:
                    def __init__(self):
                        bot = ScraperBot.__new__(ScraperBot)
                        self._intelligent_color_tracking_crop = bot._intelligent_color_tracking_crop.__get__(self, UUIDTestBot)
                        self._extract_dominant_colors = bot._extract_dominant_colors.__get__(self, UUIDTestBot)
                        self._identify_bird_type_from_colors = bot._identify_bird_type_from_colors.__get__(self, UUIDTestBot)
                        self._create_intelligent_color_mask = bot._create_intelligent_color_mask.__get__(self, UUIDTestBot)
                        self._crop_using_intelligent_mask = bot._crop_using_intelligent_mask.__get__(self, UUIDTestBot)
                        self._score_contour_for_bird_type = bot._score_contour_for_bird_type.__get__(self, UUIDTestBot)
                        self._calculate_intelligent_padding = bot._calculate_intelligent_padding.__get__(self, UUIDTestBot)
                        self._rgb_to_hsv_opencv = bot._rgb_to_hsv_opencv.__get__(self, UUIDTestBot)
                        self._extract_colors_basic = bot._extract_colors_basic.__get__(self, UUIDTestBot)
                
                uuid_bot = UUIDTestBot()
                img_cv = cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)
                
                result = uuid_bot._intelligent_color_tracking_crop(img_cv, img)
                
                if result:
                    result_path = os.path.join(debug_dir, f"uuid_test_{i+1}_result.png")
                    result.save(result_path)
                    print(f"✅ UUID test successful: {result.width}x{result.height}")
                    print(f"💾 Saved: {result_path}")
                else:
                    print("❌ UUID test failed")
                    
        except Exception as e:
            print(f"❌ Error in UUID test: {e}")
    
    print(f"\n📁 All UUID test files saved to: {debug_dir}")

def run_comprehensive_test():
    """Run comprehensive test of enhanced color tracking"""
    print("🎨 COMPREHENSIVE ENHANCED COLOR TRACKING TEST")
    print("Target UUID: f697e07b-5251-416c-85fa-5b99967decc4")
    print("=" * 70)
    
    try:
        # Test 1: Individual methods
        test_individual_methods()
        
        # Test 2: UUID specific testing
        test_with_debug_uuid()
        
        print(f"\n🏆 ENHANCED TESTING COMPLETE!")
        print("=" * 40)
        print("✅ Enhanced color tracking methods have been tested")
        print("📁 Check the generated files and debug directories")
        print("💡 The new intelligent color tracking should provide better results")
        print("   for the problematic UUID f697e07b-5251-416c-85fa-5b99967decc4")
        
    except Exception as e:
        print(f"❌ Comprehensive test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    run_comprehensive_test()
