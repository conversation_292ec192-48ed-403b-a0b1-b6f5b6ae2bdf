#!/usr/bin/env python3
"""
Bird Only Extractor - Pure Bird Image with Optional Background
=============================================================

This script extracts only the bird pixels and creates a clean image
with options for transparent, white, or no background.

Author: AI Assistant
Version: 1.0.0
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
import argparse
import logging
from pathlib import Path
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class BirdOnlyExtractor:
    """Extract only bird pixels with various background options"""
    
    def __init__(self, black_threshold: int = 35):
        self.black_threshold = black_threshold
    
    def extract_bird_only(self, image_path: str, background_type: str = "none", output_path: str = None) -> str:
        """
        Extract only bird pixels with specified background
        
        Args:
            image_path: Path to input image
            background_type: "none" (black), "white", or "transparent"
            output_path: Path for output image (optional)
            
        Returns:
            Path to processed image
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Failed to load image: {image_path}")
            
            logger.info(f"Processing: {image_path}")
            logger.info(f"Original size: {image.shape[1]}x{image.shape[0]}")
            logger.info(f"Background type: {background_type}")
            
            # Create ultra-precise bird mask
            bird_mask = self._create_ultra_precise_mask(image)
            
            # Find exact bird boundaries
            bbox = self._find_exact_bird_boundaries(bird_mask)
            
            if bbox is None:
                raise ValueError("Could not detect bird boundaries")
            
            # Extract bird with specified background
            if background_type == "transparent":
                bird_only = self._extract_with_transparency(image, bird_mask, bbox)
                output_ext = ".png"
            elif background_type == "white":
                bird_only = self._extract_with_white_background(image, bird_mask, bbox)
                output_ext = ".jpg"
            else:  # "none" - black background
                bird_only = self._extract_with_no_background(image, bird_mask, bbox)
                output_ext = ".jpg"
            
            # Enhance the result
            if background_type != "transparent":
                bird_only = self._enhance_bird_image(bird_only)
            
            # Generate output path if not provided
            if output_path is None:
                input_path = Path(image_path)
                timestamp = int(time.time())
                bg_suffix = f"_{background_type.upper()}_BG" if background_type != "none" else "_BIRD_ONLY"
                output_path = input_path.parent / f"{input_path.stem}{bg_suffix}_{timestamp}{output_ext}"
            
            # Save with appropriate format and quality
            if background_type == "transparent":
                # Save as PNG with transparency
                bird_pil = Image.fromarray(cv2.cvtColor(bird_only, cv2.COLOR_BGRA2RGBA))
                bird_pil.save(str(output_path), "PNG", optimize=True)
            else:
                # Save as high-quality JPEG
                cv2.imwrite(str(output_path), bird_only, [cv2.IMWRITE_JPEG_QUALITY, 100])
            
            logger.info(f"Bird-only image saved: {output_path}")
            logger.info(f"Final size: {bird_only.shape[1]}x{bird_only.shape[0]}")
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error extracting bird: {e}")
            raise
    
    def _create_ultra_precise_mask(self, image: np.ndarray) -> np.ndarray:
        """Create ultra-precise mask for bird detection"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # 1. Detect and exclude black/dark background
            black_mask = self._detect_background_pixels(image, gray)
            
            # 2. Detect bird using multiple methods
            color_bird = self._detect_bird_colors(hsv)
            edge_bird = self._detect_bird_edges(gray)
            texture_bird = self._detect_bird_texture(gray)
            
            # 3. Combine bird detection methods
            bird_mask = cv2.bitwise_or(color_bird, edge_bird)
            bird_mask = cv2.bitwise_or(bird_mask, texture_bird)
            
            # 4. Remove background areas from bird mask
            final_mask = cv2.bitwise_and(bird_mask, cv2.bitwise_not(black_mask))
            
            # 5. Clean up the mask
            final_mask = self._clean_bird_mask(final_mask)
            
            logger.info(f"Created mask with {np.sum(final_mask > 0)} bird pixels")
            
            return final_mask
            
        except Exception as e:
            logger.error(f"Error creating ultra-precise mask: {e}")
            raise
    
    def _detect_background_pixels(self, image: np.ndarray, gray: np.ndarray) -> np.ndarray:
        """Detect background pixels (black/dark areas)"""
        try:
            # Method 1: Grayscale threshold
            _, gray_black = cv2.threshold(gray, self.black_threshold, 255, cv2.THRESH_BINARY_INV)
            
            # Method 2: Color channel analysis
            b, g, r = cv2.split(image)
            color_black = ((b < self.black_threshold) & 
                          (g < self.black_threshold) & 
                          (r < self.black_threshold)).astype(np.uint8) * 255
            
            # Method 3: HSV value channel (brightness)
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            _, v_black = cv2.threshold(hsv[:,:,2], self.black_threshold, 255, cv2.THRESH_BINARY_INV)
            
            # Combine all methods
            combined_black = cv2.bitwise_or(gray_black, color_black)
            combined_black = cv2.bitwise_or(combined_black, v_black)
            
            # Clean up
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            combined_black = cv2.morphologyEx(combined_black, cv2.MORPH_CLOSE, kernel)
            
            return combined_black
            
        except Exception as e:
            logger.error(f"Error detecting background: {e}")
            return np.zeros_like(gray)
    
    def _detect_bird_colors(self, hsv: np.ndarray) -> np.ndarray:
        """Detect bird using comprehensive color analysis"""
        try:
            # Expanded bird color ranges
            ranges = [
                # Browns and tans
                ([3, 15, 25], [35, 255, 255]),
                # Grays (but not too dark)
                ([0, 0, 40], [180, 60, 220]),
                # Darker birds (but above background threshold)
                ([0, 0, 40], [180, 255, 140]),
                # Reds and oranges
                ([0, 40, 40], [20, 255, 255]),
                # Blues
                ([85, 25, 25], [135, 255, 255]),
                # Greens
                ([30, 25, 25], [90, 255, 255]),
                # Yellows
                ([15, 40, 40], [35, 255, 255]),
                # Whites and light colors
                ([0, 0, 140], [180, 60, 255])
            ]
            
            color_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
            
            for lower, upper in ranges:
                mask = cv2.inRange(hsv, np.array(lower), np.array(upper))
                color_mask = cv2.bitwise_or(color_mask, mask)
            
            # Clean up
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            color_mask = cv2.morphologyEx(color_mask, cv2.MORPH_OPEN, kernel)
            color_mask = cv2.morphologyEx(color_mask, cv2.MORPH_CLOSE, kernel)
            
            return color_mask
            
        except Exception as e:
            logger.error(f"Error detecting bird colors: {e}")
            return np.zeros(hsv.shape[:2], dtype=np.uint8)
    
    def _detect_bird_edges(self, gray: np.ndarray) -> np.ndarray:
        """Detect bird using edge analysis"""
        try:
            # Bilateral filter to preserve edges
            filtered = cv2.bilateralFilter(gray, 9, 75, 75)
            
            # Multi-scale Canny edge detection
            edges = []
            thresholds = [(15, 45), (30, 80), (50, 120)]
            
            for low, high in thresholds:
                edge = cv2.Canny(filtered, low, high)
                edges.append(edge)
            
            # Combine all edge maps
            combined_edges = edges[0]
            for edge in edges[1:]:
                combined_edges = cv2.bitwise_or(combined_edges, edge)
            
            # Dilate to create regions
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            edge_regions = cv2.dilate(combined_edges, kernel, iterations=2)
            
            return edge_regions
            
        except Exception as e:
            logger.error(f"Error detecting bird edges: {e}")
            return np.zeros_like(gray)
    
    def _detect_bird_texture(self, gray: np.ndarray) -> np.ndarray:
        """Detect bird using texture analysis"""
        try:
            # Local standard deviation as texture measure
            kernel = np.ones((7, 7), np.float32) / 49
            gray_float = gray.astype(np.float32)
            
            local_mean = cv2.filter2D(gray_float, -1, kernel)
            local_variance = cv2.filter2D((gray_float - local_mean)**2, -1, kernel)
            local_std = np.sqrt(local_variance)
            
            # Normalize and threshold
            std_norm = cv2.normalize(local_std, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
            _, texture_mask = cv2.threshold(std_norm, 15, 255, cv2.THRESH_BINARY)
            
            return texture_mask
            
        except Exception as e:
            logger.error(f"Error detecting bird texture: {e}")
            return np.zeros_like(gray)
    
    def _clean_bird_mask(self, mask: np.ndarray) -> np.ndarray:
        """Clean up the bird mask"""
        try:
            # Remove small noise
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return mask
            
            # Keep only significant contours
            min_area = mask.shape[0] * mask.shape[1] * 0.001  # 0.1% of image
            valid_contours = [c for c in contours if cv2.contourArea(c) > min_area]
            
            if not valid_contours:
                return mask
            
            # Find largest contour (main bird)
            largest_contour = max(valid_contours, key=cv2.contourArea)
            
            # Create clean mask
            clean_mask = np.zeros_like(mask)
            cv2.fillPoly(clean_mask, [largest_contour], 255)
            
            # Slight morphological closing
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            clean_mask = cv2.morphologyEx(clean_mask, cv2.MORPH_CLOSE, kernel)
            
            return clean_mask
            
        except Exception as e:
            logger.error(f"Error cleaning mask: {e}")
            return mask
    
    def _find_exact_bird_boundaries(self, mask: np.ndarray) -> tuple:
        """Find exact boundaries of the bird"""
        try:
            points = cv2.findNonZero(mask)
            if points is None:
                return None
            
            x, y, w, h = cv2.boundingRect(points)
            logger.info(f"Exact bird boundaries: x={x}, y={y}, w={w}, h={h}")
            
            return (x, y, w, h)
            
        except Exception as e:
            logger.error(f"Error finding boundaries: {e}")
            return None
    
    def _extract_with_no_background(self, image: np.ndarray, mask: np.ndarray, bbox: tuple) -> np.ndarray:
        """Extract bird with black background (no background)"""
        x, y, w, h = bbox
        
        # Crop to bounding box
        cropped_image = image[y:y+h, x:x+w]
        cropped_mask = mask[y:y+h, x:x+w]
        
        # Apply mask
        mask_3ch = cv2.cvtColor(cropped_mask, cv2.COLOR_GRAY2BGR)
        result = cv2.bitwise_and(cropped_image, mask_3ch)
        
        return result
    
    def _extract_with_white_background(self, image: np.ndarray, mask: np.ndarray, bbox: tuple) -> np.ndarray:
        """Extract bird with white background"""
        x, y, w, h = bbox
        
        # Crop to bounding box
        cropped_image = image[y:y+h, x:x+w]
        cropped_mask = mask[y:y+h, x:x+w]
        
        # Create white background
        white_bg = np.ones_like(cropped_image) * 255
        
        # Apply mask to bird
        mask_3ch = cv2.cvtColor(cropped_mask, cv2.COLOR_GRAY2BGR)
        bird_part = cv2.bitwise_and(cropped_image, mask_3ch)
        
        # Apply inverse mask to white background
        inv_mask_3ch = cv2.bitwise_not(mask_3ch)
        white_part = cv2.bitwise_and(white_bg, inv_mask_3ch)
        
        # Combine bird and white background
        result = cv2.add(bird_part, white_part)
        
        return result
    
    def _extract_with_transparency(self, image: np.ndarray, mask: np.ndarray, bbox: tuple) -> np.ndarray:
        """Extract bird with transparent background"""
        x, y, w, h = bbox
        
        # Crop to bounding box
        cropped_image = image[y:y+h, x:x+w]
        cropped_mask = mask[y:y+h, x:x+w]
        
        # Create BGRA image (with alpha channel)
        bgra_image = cv2.cvtColor(cropped_image, cv2.COLOR_BGR2BGRA)
        
        # Set alpha channel based on mask
        bgra_image[:, :, 3] = cropped_mask
        
        return bgra_image
    
    def _enhance_bird_image(self, image: np.ndarray) -> np.ndarray:
        """Enhance the extracted bird image"""
        try:
            # Convert to PIL for enhancement
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(1.4)
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.2)
            
            # Enhance color
            enhancer = ImageEnhance.Color(pil_image)
            pil_image = enhancer.enhance(1.1)
            
            # Convert back to OpenCV
            enhanced = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return enhanced
            
        except Exception as e:
            logger.error(f"Error enhancing image: {e}")
            return image


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Bird Only Extractor - Pure Bird Image with Optional Background",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Extract bird only (black background)
  python bird_only_extractor.py --input image.jpg --background none
  
  # Extract bird with white background
  python bird_only_extractor.py --input image.jpg --background white
  
  # Extract bird with transparent background (PNG)
  python bird_only_extractor.py --input image.jpg --background transparent
        """
    )
    
    parser.add_argument('--input', '-i', type=str, required=True, help='Input image path')
    parser.add_argument('--output', '-o', type=str, help='Output image path (optional)')
    parser.add_argument('--background', '-b', type=str, choices=['none', 'white', 'transparent'], 
                       default='none', help='Background type (default: none)')
    parser.add_argument('--black-threshold', type=int, default=35,
                       help='Threshold for detecting black pixels (default: 35)')
    
    args = parser.parse_args()
    
    # Create bird extractor instance
    extractor = BirdOnlyExtractor(black_threshold=args.black_threshold)
    
    # Process the image
    try:
        result = extractor.extract_bird_only(args.input, args.background, args.output)
        print(f"✅ Successfully extracted bird: {result}")
    except Exception as e:
        print(f"❌ Failed to extract bird: {e}")


if __name__ == "__main__":
    main()
