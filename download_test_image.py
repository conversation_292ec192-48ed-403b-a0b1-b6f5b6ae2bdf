#!/usr/bin/env python3
"""
Script untuk download image burung dari URL untuk testing
"""

import requests
from PIL import Image
import io

def download_bird_image():
    """Download image burung untuk testing"""
    try:
        # Buat image test berdasarkan deskripsi image yang diberikan
        # Image burung coklat-putih di atas kayu
        
        # Karena tidak bisa download langsung, saya akan buat simulasi image serupa
        img = Image.new('RGB', (900, 600), (220, 200, 180))  # Background kayu
        
        from PIL import ImageDraw
        draw = ImageDraw.Draw(img)
        
        # Gambar kayu/deck background
        for i in range(0, 900, 120):
            draw.rectangle([i, 0, i+2, 600], fill=(180, 160, 140))
        
        for i in range(0, 600, 80):
            draw.rectangle([0, i, 900, i+2], fill=(180, 160, 140))
        
        # Gambar burung di tengah
        bird_x, bird_y = 450, 200
        
        # Body burung (coklat)
        draw.ellipse([bird_x-60, bird_y+20, bird_x+60, bird_y+100], fill=(101, 67, 33))
        
        # Ke<PERSON>a burung (coklat gelap)
        draw.ellipse([bird_x-40, bird_y-20, bird_x+40, bird_y+40], fill=(80, 50, 25))
        
        # Dada putih
        draw.ellipse([bird_x-35, bird_y+10, bird_x+35, bird_y+70], fill=(245, 240, 235))
        
        # Mata
        draw.ellipse([bird_x-15, bird_y-5, bird_x-5, bird_y+5], fill=(20, 20, 20))
        draw.ellipse([bird_x-12, bird_y-2, bird_x-8, bird_y+2], fill=(255, 255, 255))
        
        # Paruh
        draw.polygon([
            (bird_x-45, bird_y+5),
            (bird_x-55, bird_y+10),
            (bird_x-45, bird_y+15)
        ], fill=(255, 200, 100))
        
        # Kaki
        draw.line([bird_x-20, bird_y+100, bird_x-20, bird_y+130], fill=(255, 200, 100), width=3)
        draw.line([bird_x+20, bird_y+100, bird_x+20, bird_y+130], fill=(255, 200, 100), width=3)
        
        # Sayap
        draw.ellipse([bird_x+10, bird_y+30, bird_x+50, bird_y+80], fill=(90, 60, 30))
        
        # Tambahkan beberapa detail kayu dan bayangan
        # Bayangan burung
        shadow_offset = 20
        draw.ellipse([
            bird_x-60+shadow_offset, bird_y+100+shadow_offset,
            bird_x+60+shadow_offset, bird_y+120+shadow_offset
        ], fill=(150, 130, 110))
        
        # Save image
        img.save("test_bird_on_wood.png")
        print("✅ Test bird image created: test_bird_on_wood.png")
        
        return "test_bird_on_wood.png"
        
    except Exception as e:
        print(f"❌ Error creating test image: {e}")
        return None

if __name__ == "__main__":
    download_bird_image()
