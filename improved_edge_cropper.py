#!/usr/bin/env python3
"""
Improved Edge Cropper - Remove Black Padding from All Sides
===========================================================

This script removes black padding from all edges (top, bottom, left, right)
while preserving 100% of the bird pixels.

Author: AI Assistant
Version: 1.0.0
"""

import cv2
import numpy as np
import argparse
import logging
from pathlib import Path
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ImprovedEdgeCropper:
    """Remove black padding from all edges while preserving all bird pixels"""
    
    def __init__(self, black_threshold: int = 30):
        self.black_threshold = black_threshold
    
    def remove_all_edge_padding(self, image_path: str, output_path: str = None) -> str:
        """
        Remove black padding from ALL edges (top, bottom, left, right)
        
        Args:
            image_path: Path to input image
            output_path: Path for output image (optional)
            
        Returns:
            Path to processed image
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Failed to load image: {image_path}")
            
            logger.info(f"Processing: {image_path}")
            logger.info(f"Original size: {image.shape[1]}x{image.shape[0]}")
            
            # Find content boundaries by scanning from each edge
            content_bounds = self._scan_all_edges_for_content(image)
            
            # Crop to the detected content boundaries
            cropped_image = self._crop_to_content_bounds(image, content_bounds)
            
            # Generate output path if not provided
            if output_path is None:
                input_path = Path(image_path)
                timestamp = int(time.time())
                output_path = input_path.parent / f"{input_path.stem}_ALL_EDGES_CROPPED_{timestamp}.jpg"
            
            # Save with maximum quality
            cv2.imwrite(str(output_path), cropped_image, [cv2.IMWRITE_JPEG_QUALITY, 100])
            
            logger.info(f"All edges cropped image saved: {output_path}")
            logger.info(f"Final size: {cropped_image.shape[1]}x{cropped_image.shape[0]}")
            
            # Calculate padding removed from each side
            original_h, original_w = image.shape[:2]
            final_h, final_w = cropped_image.shape[:2]
            
            top_removed = content_bounds[1]
            left_removed = content_bounds[0]
            bottom_removed = original_h - (content_bounds[1] + final_h)
            right_removed = original_w - (content_bounds[0] + final_w)
            
            logger.info(f"Padding removed - Top: {top_removed}, Bottom: {bottom_removed}, Left: {left_removed}, Right: {right_removed}")
            logger.info(f"Total padding pixels removed: {(original_w * original_h) - (final_w * final_h)}")
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error in improved edge cropping: {e}")
            raise
    
    def _scan_all_edges_for_content(self, image: np.ndarray) -> tuple:
        """
        Scan from all edges inward to find where content begins
        Returns (left, top, width, height) of content area
        """
        try:
            height, width = image.shape[:2]
            
            # Scan from left edge
            left_bound = self._scan_from_left(image)
            
            # Scan from right edge  
            right_bound = self._scan_from_right(image)
            
            # Scan from top edge
            top_bound = self._scan_from_top(image)
            
            # Scan from bottom edge
            bottom_bound = self._scan_from_bottom(image)
            
            # Calculate content area
            content_left = left_bound
            content_top = top_bound
            content_width = right_bound - left_bound
            content_height = bottom_bound - top_bound
            
            logger.info(f"Content boundaries detected:")
            logger.info(f"  Left edge content starts at: {left_bound}")
            logger.info(f"  Right edge content ends at: {right_bound}")
            logger.info(f"  Top edge content starts at: {top_bound}")
            logger.info(f"  Bottom edge content ends at: {bottom_bound}")
            logger.info(f"  Content area: x={content_left}, y={content_top}, w={content_width}, h={content_height}")
            
            return (content_left, content_top, content_width, content_height)
            
        except Exception as e:
            logger.error(f"Error scanning edges: {e}")
            return (0, 0, image.shape[1], image.shape[0])
    
    def _scan_from_left(self, image: np.ndarray) -> int:
        """Scan from left edge to find where content begins"""
        try:
            height, width = image.shape[:2]
            
            for x in range(width):
                # Check if this column has significant content
                column = image[:, x]
                if self._column_has_content(column):
                    logger.debug(f"Left content starts at column {x}")
                    return x
            
            # If no content found, return 0
            return 0
            
        except Exception as e:
            logger.error(f"Error scanning from left: {e}")
            return 0
    
    def _scan_from_right(self, image: np.ndarray) -> int:
        """Scan from right edge to find where content ends"""
        try:
            height, width = image.shape[:2]
            
            for x in range(width - 1, -1, -1):
                # Check if this column has significant content
                column = image[:, x]
                if self._column_has_content(column):
                    logger.debug(f"Right content ends at column {x + 1}")
                    return x + 1
            
            # If no content found, return width
            return width
            
        except Exception as e:
            logger.error(f"Error scanning from right: {e}")
            return image.shape[1]
    
    def _scan_from_top(self, image: np.ndarray) -> int:
        """Scan from top edge to find where content begins"""
        try:
            height, width = image.shape[:2]
            
            for y in range(height):
                # Check if this row has significant content
                row = image[y, :]
                if self._row_has_content(row):
                    logger.debug(f"Top content starts at row {y}")
                    return y
            
            # If no content found, return 0
            return 0
            
        except Exception as e:
            logger.error(f"Error scanning from top: {e}")
            return 0
    
    def _scan_from_bottom(self, image: np.ndarray) -> int:
        """Scan from bottom edge to find where content ends"""
        try:
            height, width = image.shape[:2]
            
            for y in range(height - 1, -1, -1):
                # Check if this row has significant content
                row = image[y, :]
                if self._row_has_content(row):
                    logger.debug(f"Bottom content ends at row {y + 1}")
                    return y + 1
            
            # If no content found, return height
            return height
            
        except Exception as e:
            logger.error(f"Error scanning from bottom: {e}")
            return image.shape[0]
    
    def _column_has_content(self, column: np.ndarray) -> bool:
        """Check if a column has significant content (not just black padding)"""
        try:
            # Convert to grayscale if needed
            if len(column.shape) == 3:
                gray_column = cv2.cvtColor(column.reshape(-1, 1, 3), cv2.COLOR_BGR2GRAY).flatten()
            else:
                gray_column = column.flatten()
            
            # Count pixels above threshold
            content_pixels = np.sum(gray_column > self.black_threshold)
            total_pixels = len(gray_column)
            
            # Consider it content if more than 10% of pixels are above threshold
            content_ratio = content_pixels / total_pixels
            return content_ratio > 0.1
            
        except Exception as e:
            logger.error(f"Error checking column content: {e}")
            return True  # Conservative: assume it has content
    
    def _row_has_content(self, row: np.ndarray) -> bool:
        """Check if a row has significant content (not just black padding)"""
        try:
            # Convert to grayscale if needed
            if len(row.shape) == 3:
                gray_row = cv2.cvtColor(row.reshape(1, -1, 3), cv2.COLOR_BGR2GRAY).flatten()
            else:
                gray_row = row.flatten()
            
            # Count pixels above threshold
            content_pixels = np.sum(gray_row > self.black_threshold)
            total_pixels = len(gray_row)
            
            # Consider it content if more than 10% of pixels are above threshold
            content_ratio = content_pixels / total_pixels
            return content_ratio > 0.1
            
        except Exception as e:
            logger.error(f"Error checking row content: {e}")
            return True  # Conservative: assume it has content
    
    def _crop_to_content_bounds(self, image: np.ndarray, bounds: tuple) -> np.ndarray:
        """Crop image to the specified content bounds"""
        try:
            left, top, width, height = bounds
            
            # Ensure bounds are valid
            left = max(0, left)
            top = max(0, top)
            right = min(image.shape[1], left + width)
            bottom = min(image.shape[0], top + height)
            
            # Crop the image
            cropped = image[top:bottom, left:right]
            
            logger.info(f"Cropped from {image.shape[1]}x{image.shape[0]} to {cropped.shape[1]}x{cropped.shape[0]}")
            
            return cropped
            
        except Exception as e:
            logger.error(f"Error cropping to bounds: {e}")
            return image
    
    def analyze_edge_padding(self, image_path: str) -> dict:
        """Analyze edge padding on all sides"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                return {"error": "Could not load image"}
            
            height, width = image.shape[:2]
            
            # Scan each edge
            left_bound = self._scan_from_left(image)
            right_bound = self._scan_from_right(image)
            top_bound = self._scan_from_top(image)
            bottom_bound = self._scan_from_bottom(image)
            
            # Calculate padding on each side
            left_padding = left_bound
            right_padding = width - right_bound
            top_padding = top_bound
            bottom_padding = height - bottom_bound
            
            # Calculate final dimensions
            final_width = right_bound - left_bound
            final_height = bottom_bound - top_bound
            
            analysis = {
                "original_size": f"{width}x{height}",
                "left_padding": left_padding,
                "right_padding": right_padding,
                "top_padding": top_padding,
                "bottom_padding": bottom_padding,
                "total_padding_pixels": (width * height) - (final_width * final_height),
                "final_size": f"{final_width}x{final_height}",
                "content_bounds": f"({left_bound}, {top_bound}) to ({right_bound}, {bottom_bound})"
            }
            
            return analysis
            
        except Exception as e:
            return {"error": str(e)}


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Improved Edge Cropper - Remove Black Padding from All Sides",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Remove padding from all edges
  python improved_edge_cropper.py --input 00_original_PERFECT_CROP_1px.jpg
  
  # Analyze edge padding
  python improved_edge_cropper.py --input image.jpg --analyze-only
  
  # Custom black threshold
  python improved_edge_cropper.py --input image.jpg --black-threshold 35
        """
    )
    
    parser.add_argument('--input', '-i', type=str, required=True, help='Input image path')
    parser.add_argument('--output', '-o', type=str, help='Output image path (optional)')
    parser.add_argument('--black-threshold', type=int, default=30,
                       help='Threshold for detecting black pixels (default: 30)')
    parser.add_argument('--analyze-only', action='store_true',
                       help='Only analyze the image, do not process')
    
    args = parser.parse_args()
    
    # Create improved edge cropper instance
    cropper = ImprovedEdgeCropper(black_threshold=args.black_threshold)
    
    if args.analyze_only:
        # Just analyze the image
        analysis = cropper.analyze_edge_padding(args.input)
        print("\n" + "="*60)
        print("EDGE PADDING ANALYSIS")
        print("="*60)
        for key, value in analysis.items():
            print(f"{key.replace('_', ' ').title()}: {value}")
        print("="*60)
    else:
        # Process the image
        try:
            result = cropper.remove_all_edge_padding(args.input, args.output)
            print(f"✅ Successfully removed padding from all edges: {result}")
        except Exception as e:
            print(f"❌ Failed to process image: {e}")


if __name__ == "__main__":
    main()
