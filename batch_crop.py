#!/usr/bin/env python3
"""
Batch Processing Script for Bird Image Cropping
===============================================

This script provides advanced batch processing capabilities for the
bird cropping bot with progress tracking, parallel processing, and
comprehensive reporting.

Author: AI Assistant
Version: 1.0.0
"""

import os
import sys
import time
import json
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Tuple
import argparse
import logging
from dataclasses import dataclass, asdict

# Import the main cropping bot
from bird_cropping_bot import IntelligentCropper, CroppingConfig, load_config

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('batch_cropping.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class BatchResult:
    """Results from batch processing"""
    total_images: int = 0
    successful_crops: int = 0
    failed_crops: int = 0
    processing_time: float = 0.0
    successful_files: List[str] = None
    failed_files: List[str] = None
    
    def __post_init__(self):
        if self.successful_files is None:
            self.successful_files = []
        if self.failed_files is None:
            self.failed_files = []

class AdvancedBatchProcessor:
    """Advanced batch processor with parallel processing and progress tracking"""
    
    def __init__(self, config: CroppingConfig, max_workers: int = 4):
        self.config = config
        self.max_workers = max_workers
        self.cropper = IntelligentCropper(config)
    
    def process_directory(self, input_dir: str, recursive: bool = False, 
                         file_patterns: List[str] = None) -> BatchResult:
        """
        Process all images in a directory with advanced features
        
        Args:
            input_dir: Input directory path
            recursive: Whether to search subdirectories
            file_patterns: File patterns to match
            
        Returns:
            BatchResult object with processing statistics
        """
        start_time = time.time()
        result = BatchResult()
        
        # Find all image files
        image_files = self._find_image_files(input_dir, recursive, file_patterns)
        result.total_images = len(image_files)
        
        if not image_files:
            logger.warning(f"No image files found in {input_dir}")
            return result
        
        logger.info(f"Found {len(image_files)} images to process")
        logger.info(f"Using {self.max_workers} worker threads")
        
        # Process images with thread pool
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # Submit all tasks
            future_to_file = {
                executor.submit(self._process_single_image, str(img_file)): img_file 
                for img_file in image_files
            }
            
            # Process completed tasks
            for i, future in enumerate(as_completed(future_to_file), 1):
                img_file = future_to_file[future]
                
                try:
                    crop_result = future.result()
                    if crop_result:
                        result.successful_crops += 1
                        result.successful_files.append(crop_result)
                        logger.info(f"[{i}/{len(image_files)}] ✓ {img_file.name}")
                    else:
                        result.failed_crops += 1
                        result.failed_files.append(str(img_file))
                        logger.warning(f"[{i}/{len(image_files)}] ✗ {img_file.name}")
                        
                except Exception as e:
                    result.failed_crops += 1
                    result.failed_files.append(str(img_file))
                    logger.error(f"[{i}/{len(image_files)}] ✗ {img_file.name}: {e}")
                
                # Progress update
                if i % 10 == 0 or i == len(image_files):
                    progress = (i / len(image_files)) * 100
                    logger.info(f"Progress: {progress:.1f}% ({i}/{len(image_files)})")
        
        result.processing_time = time.time() - start_time
        return result
    
    def _find_image_files(self, input_dir: str, recursive: bool, 
                         file_patterns: List[str] = None) -> List[Path]:
        """Find all image files in directory"""
        if file_patterns is None:
            file_patterns = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp']
        
        input_path = Path(input_dir)
        if not input_path.exists():
            logger.error(f"Input directory does not exist: {input_dir}")
            return []
        
        image_files = []
        
        if recursive:
            # Search recursively
            for pattern in file_patterns:
                image_files.extend(input_path.rglob(pattern))
                image_files.extend(input_path.rglob(pattern.upper()))
        else:
            # Search only in current directory
            for pattern in file_patterns:
                image_files.extend(input_path.glob(pattern))
                image_files.extend(input_path.glob(pattern.upper()))
        
        # Remove duplicates and sort
        image_files = sorted(list(set(image_files)))
        return image_files
    
    def _process_single_image(self, image_path: str) -> str:
        """Process a single image (thread-safe)"""
        try:
            return self.cropper.crop_image(image_path)
        except Exception as e:
            logger.error(f"Error processing {image_path}: {e}")
            return None
    
    def generate_report(self, result: BatchResult, output_path: str = None) -> str:
        """Generate detailed processing report"""
        if output_path is None:
            output_path = f"batch_report_{int(time.time())}.json"
        
        # Calculate statistics
        success_rate = (result.successful_crops / result.total_images * 100) if result.total_images > 0 else 0
        avg_time_per_image = result.processing_time / result.total_images if result.total_images > 0 else 0
        
        report = {
            'summary': {
                'total_images': result.total_images,
                'successful_crops': result.successful_crops,
                'failed_crops': result.failed_crops,
                'success_rate': f"{success_rate:.1f}%",
                'total_processing_time': f"{result.processing_time:.2f} seconds",
                'average_time_per_image': f"{avg_time_per_image:.2f} seconds"
            },
            'successful_files': result.successful_files,
            'failed_files': result.failed_files,
            'configuration': asdict(self.config),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        try:
            with open(output_path, 'w') as f:
                json.dump(report, f, indent=2, default=str)
            
            logger.info(f"Report saved to: {output_path}")
            return output_path
        except Exception as e:
            logger.error(f"Error saving report: {e}")
            return None


def main():
    """Main entry point for batch processing"""
    parser = argparse.ArgumentParser(
        description="Advanced Batch Processing for Bird Image Cropping",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process all images in a directory
  python batch_crop.py --input-dir ./screenshots
  
  # Process recursively with 8 threads
  python batch_crop.py --input-dir ./images --recursive --workers 8
  
  # Process with custom configuration
  python batch_crop.py --input-dir ./images --config custom_config.json
  
  # Generate detailed report
  python batch_crop.py --input-dir ./images --report batch_report.json
        """
    )
    
    parser.add_argument('--input-dir', '-d', type=str, required=True,
                       help='Input directory containing images')
    parser.add_argument('--recursive', '-r', action='store_true',
                       help='Search subdirectories recursively')
    parser.add_argument('--workers', '-w', type=int, default=4,
                       help='Number of worker threads (default: 4)')
    parser.add_argument('--config', '-c', type=str, default='cropping_config.json',
                       help='Configuration file path')
    parser.add_argument('--report', type=str,
                       help='Output path for detailed report (JSON)')
    parser.add_argument('--patterns', '-p', nargs='+',
                       help='File patterns to match (e.g., *.jpg *.png)')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug logging')
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Load configuration
    config = load_config(args.config)
    
    # Create batch processor
    processor = AdvancedBatchProcessor(config, max_workers=args.workers)
    
    # Process images
    logger.info("Starting batch processing...")
    result = processor.process_directory(
        args.input_dir, 
        recursive=args.recursive,
        file_patterns=args.patterns
    )
    
    # Print summary
    print("\n" + "="*60)
    print("BATCH PROCESSING SUMMARY")
    print("="*60)
    print(f"Total images processed: {result.total_images}")
    print(f"Successful crops: {result.successful_crops}")
    print(f"Failed crops: {result.failed_crops}")
    
    if result.total_images > 0:
        success_rate = (result.successful_crops / result.total_images) * 100
        print(f"Success rate: {success_rate:.1f}%")
    
    print(f"Processing time: {result.processing_time:.2f} seconds")
    
    if result.total_images > 0:
        avg_time = result.processing_time / result.total_images
        print(f"Average time per image: {avg_time:.2f} seconds")
    
    # Generate report if requested
    if args.report:
        report_path = processor.generate_report(result, args.report)
        if report_path:
            print(f"Detailed report saved to: {report_path}")
    
    print("="*60)


if __name__ == "__main__":
    main()
