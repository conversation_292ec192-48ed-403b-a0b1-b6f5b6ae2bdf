#!/usr/bin/env python3
"""
Content Boundary Cropper - Find Exact Bird Content Boundaries
=============================================================

This script finds the exact boundaries of bird content by detecting
where meaningful image content begins and ends, preserving 100% of bird pixels.

Author: AI Assistant
Version: 1.0.0
"""

import cv2
import numpy as np
import argparse
import logging
from pathlib import Path
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ContentBoundaryCropper:
    """Find exact content boundaries and crop to preserve all bird pixels"""
    
    def __init__(self, content_threshold: int = 30):
        self.content_threshold = content_threshold
    
    def crop_to_content_boundaries(self, image_path: str, output_path: str = None) -> str:
        """
        Crop to exact content boundaries, preserving ALL bird pixels
        
        Args:
            image_path: Path to input image
            output_path: Path for output image (optional)
            
        Returns:
            Path to processed image
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Failed to load image: {image_path}")
            
            logger.info(f"Processing: {image_path}")
            logger.info(f"Original size: {image.shape[1]}x{image.shape[0]}")
            
            # Find content boundaries using multiple methods
            content_bounds = self._find_content_boundaries(image)
            
            # Crop to content boundaries
            cropped_image = self._crop_to_boundaries(image, content_bounds)
            
            # Generate output path if not provided
            if output_path is None:
                input_path = Path(image_path)
                timestamp = int(time.time())
                output_path = input_path.parent / f"{input_path.stem}_CONTENT_BOUNDARIES_{timestamp}.jpg"
            
            # Save with maximum quality
            cv2.imwrite(str(output_path), cropped_image, [cv2.IMWRITE_JPEG_QUALITY, 100])
            
            logger.info(f"Content boundary cropped image saved: {output_path}")
            logger.info(f"Final size: {cropped_image.shape[1]}x{cropped_image.shape[0]}")
            
            # Calculate savings
            original_pixels = image.shape[0] * image.shape[1]
            final_pixels = cropped_image.shape[0] * cropped_image.shape[1]
            pixels_removed = original_pixels - final_pixels
            
            logger.info(f"Padding pixels removed: {pixels_removed}")
            logger.info(f"Content pixels preserved: 100%")
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error in content boundary cropping: {e}")
            raise
    
    def _find_content_boundaries(self, image: np.ndarray) -> tuple:
        """
        Find the exact boundaries where meaningful content begins and ends
        """
        try:
            # Method 1: Variance-based content detection
            variance_bounds = self._find_variance_boundaries(image)
            
            # Method 2: Edge-based content detection  
            edge_bounds = self._find_edge_boundaries(image)
            
            # Method 3: Color variation-based content detection
            color_bounds = self._find_color_variation_boundaries(image)
            
            # Combine all methods to find the tightest bounds that contain all content
            combined_bounds = self._combine_boundary_methods([
                variance_bounds, edge_bounds, color_bounds
            ])
            
            logger.info(f"Content boundaries: x={combined_bounds[0]}, y={combined_bounds[1]}, "
                       f"right={combined_bounds[2]}, bottom={combined_bounds[3]}")
            
            return combined_bounds
            
        except Exception as e:
            logger.error(f"Error finding content boundaries: {e}")
            # Return original image bounds if error
            return (0, 0, image.shape[1], image.shape[0])
    
    def _find_variance_boundaries(self, image: np.ndarray) -> tuple:
        """Find boundaries based on pixel variance (texture/detail)"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Calculate local variance
            kernel = np.ones((5, 5), np.float32) / 25
            gray_float = gray.astype(np.float32)
            local_mean = cv2.filter2D(gray_float, -1, kernel)
            local_variance = cv2.filter2D((gray_float - local_mean)**2, -1, kernel)
            
            # Threshold to find areas with significant variance
            _, variance_mask = cv2.threshold(local_variance.astype(np.uint8), 10, 255, cv2.THRESH_BINARY)
            
            return self._get_bounds_from_mask(variance_mask)
            
        except Exception as e:
            logger.error(f"Error in variance boundary detection: {e}")
            return (0, 0, image.shape[1], image.shape[0])
    
    def _find_edge_boundaries(self, image: np.ndarray) -> tuple:
        """Find boundaries based on edge content"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply bilateral filter to preserve edges
            filtered = cv2.bilateralFilter(gray, 9, 75, 75)
            
            # Detect edges
            edges = cv2.Canny(filtered, 20, 60)
            
            # Dilate edges to create content regions
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            edge_regions = cv2.dilate(edges, kernel, iterations=1)
            
            return self._get_bounds_from_mask(edge_regions)
            
        except Exception as e:
            logger.error(f"Error in edge boundary detection: {e}")
            return (0, 0, image.shape[1], image.shape[0])
    
    def _find_color_variation_boundaries(self, image: np.ndarray) -> tuple:
        """Find boundaries based on color variation"""
        try:
            # Convert to LAB color space for better color analysis
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            
            # Calculate color variation in each channel
            content_mask = np.zeros(image.shape[:2], dtype=np.uint8)
            
            for channel in range(3):
                # Calculate local standard deviation
                kernel = np.ones((7, 7), np.float32) / 49
                channel_data = lab[:, :, channel].astype(np.float32)
                local_mean = cv2.filter2D(channel_data, -1, kernel)
                local_variance = cv2.filter2D((channel_data - local_mean)**2, -1, kernel)
                local_std = np.sqrt(local_variance)
                
                # Threshold to find areas with color variation
                _, channel_mask = cv2.threshold(local_std.astype(np.uint8), 5, 255, cv2.THRESH_BINARY)
                content_mask = cv2.bitwise_or(content_mask, channel_mask)
            
            return self._get_bounds_from_mask(content_mask)
            
        except Exception as e:
            logger.error(f"Error in color variation boundary detection: {e}")
            return (0, 0, image.shape[1], image.shape[0])
    
    def _get_bounds_from_mask(self, mask: np.ndarray) -> tuple:
        """Get bounding box from a binary mask"""
        try:
            # Find all non-zero points
            points = cv2.findNonZero(mask)
            
            if points is None:
                # No content found, return full image bounds
                return (0, 0, mask.shape[1], mask.shape[0])
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(points)
            
            # Return as (left, top, right, bottom)
            return (x, y, x + w, y + h)
            
        except Exception as e:
            logger.error(f"Error getting bounds from mask: {e}")
            return (0, 0, mask.shape[1], mask.shape[0])
    
    def _combine_boundary_methods(self, bounds_list: list) -> tuple:
        """
        Combine multiple boundary detection methods to find the optimal bounds
        Takes the union of all detected content areas
        """
        try:
            valid_bounds = [b for b in bounds_list if b is not None]
            
            if not valid_bounds:
                logger.warning("No valid bounds found")
                return (0, 0, 0, 0)
            
            # Find the union of all bounds (most inclusive)
            min_x = min(bounds[0] for bounds in valid_bounds)
            min_y = min(bounds[1] for bounds in valid_bounds)
            max_x = max(bounds[2] for bounds in valid_bounds)
            max_y = max(bounds[3] for bounds in valid_bounds)
            
            return (min_x, min_y, max_x, max_y)
            
        except Exception as e:
            logger.error(f"Error combining boundary methods: {e}")
            return (0, 0, 0, 0)
    
    def _crop_to_boundaries(self, image: np.ndarray, bounds: tuple) -> np.ndarray:
        """Crop image to the specified boundaries"""
        try:
            left, top, right, bottom = bounds
            
            # Ensure bounds are within image
            left = max(0, left)
            top = max(0, top)
            right = min(image.shape[1], right)
            bottom = min(image.shape[0], bottom)
            
            # Crop the image
            cropped = image[top:bottom, left:right]
            
            logger.info(f"Cropped from {image.shape[1]}x{image.shape[0]} to {cropped.shape[1]}x{cropped.shape[0]}")
            
            return cropped
            
        except Exception as e:
            logger.error(f"Error cropping to boundaries: {e}")
            return image
    
    def analyze_content_boundaries(self, image_path: str) -> dict:
        """Analyze the image to show detected content boundaries"""
        try:
            image = cv2.imread(image_path)
            if image is None:
                return {"error": "Could not load image"}
            
            # Find boundaries using each method
            variance_bounds = self._find_variance_boundaries(image)
            edge_bounds = self._find_edge_boundaries(image)
            color_bounds = self._find_color_variation_boundaries(image)
            
            # Combine methods
            combined_bounds = self._combine_boundary_methods([
                variance_bounds, edge_bounds, color_bounds
            ])
            
            # Calculate final crop dimensions
            left, top, right, bottom = combined_bounds
            final_width = right - left
            final_height = bottom - top
            
            analysis = {
                "original_size": f"{image.shape[1]}x{image.shape[0]}",
                "variance_bounds": f"({variance_bounds[0]}, {variance_bounds[1]}) to ({variance_bounds[2]}, {variance_bounds[3]})",
                "edge_bounds": f"({edge_bounds[0]}, {edge_bounds[1]}) to ({edge_bounds[2]}, {edge_bounds[3]})",
                "color_bounds": f"({color_bounds[0]}, {color_bounds[1]}) to ({color_bounds[2]}, {color_bounds[3]})",
                "combined_bounds": f"({left}, {top}) to ({right}, {bottom})",
                "final_size": f"{final_width}x{final_height}",
                "pixels_removed": (image.shape[0] * image.shape[1]) - (final_width * final_height)
            }
            
            return analysis
            
        except Exception as e:
            return {"error": str(e)}


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Content Boundary Cropper - Find Exact Bird Content Boundaries",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Crop to exact content boundaries
  python content_boundary_cropper.py --input 00_original_PERFECT_CROP_1px.jpg
  
  # Analyze content boundaries
  python content_boundary_cropper.py --input image.jpg --analyze-only
  
  # Custom content threshold
  python content_boundary_cropper.py --input image.jpg --content-threshold 40
        """
    )
    
    parser.add_argument('--input', '-i', type=str, required=True, help='Input image path')
    parser.add_argument('--output', '-o', type=str, help='Output image path (optional)')
    parser.add_argument('--content-threshold', type=int, default=30,
                       help='Threshold for content detection (default: 30)')
    parser.add_argument('--analyze-only', action='store_true',
                       help='Only analyze the image, do not process')
    
    args = parser.parse_args()
    
    # Create content boundary cropper instance
    cropper = ContentBoundaryCropper(content_threshold=args.content_threshold)
    
    if args.analyze_only:
        # Just analyze the image
        analysis = cropper.analyze_content_boundaries(args.input)
        print("\n" + "="*60)
        print("CONTENT BOUNDARY ANALYSIS")
        print("="*60)
        for key, value in analysis.items():
            print(f"{key.replace('_', ' ').title()}: {value}")
        print("="*60)
    else:
        # Process the image
        try:
            result = cropper.crop_to_content_boundaries(args.input, args.output)
            print(f"✅ Successfully cropped to content boundaries: {result}")
        except Exception as e:
            print(f"❌ Failed to process image: {e}")


if __name__ == "__main__":
    main()
