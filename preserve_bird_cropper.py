#!/usr/bin/env python3
"""
Preserve Bird Cropper - Remove Only Black Background Padding
===========================================================

This script removes ONLY black background padding while preserving
100% of the bird pixels without any color detection or modification.

Author: AI Assistant
Version: 1.0.0
"""

import cv2
import numpy as np
import argparse
import logging
from pathlib import Path
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PreserveBirdCropper:
    """Remove only black background padding while preserving all bird pixels"""
    
    def __init__(self, black_threshold: int = 25):
        self.black_threshold = black_threshold
    
    def remove_black_padding_only(self, image_path: str, output_path: str = None) -> str:
        """
        Remove only black background padding, preserve ALL bird pixels
        
        Args:
            image_path: Path to input image
            output_path: Path for output image (optional)
            
        Returns:
            Path to processed image
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                raise ValueError(f"Failed to load image: {image_path}")
            
            logger.info(f"Processing: {image_path}")
            logger.info(f"Original size: {image.shape[1]}x{image.shape[0]}")
            
            # Step 1: Detect ONLY pure black background pixels (padding)
            black_padding_mask = self._detect_pure_black_padding(image)
            
            # Step 2: Find the content area (everything that's NOT black padding)
            content_mask = cv2.bitwise_not(black_padding_mask)
            
            # Step 3: Find the minimal bounding box around ALL content
            content_bbox = self._find_content_bounding_box(content_mask)
            
            if content_bbox is None:
                logger.warning("No content detected, using original image")
                content_bbox = (0, 0, image.shape[1], image.shape[0])
            
            # Step 4: Crop to the content bounding box (preserves ALL bird pixels)
            cropped_image = self._crop_to_content_area(image, content_bbox)
            
            # Generate output path if not provided
            if output_path is None:
                input_path = Path(image_path)
                timestamp = int(time.time())
                output_path = input_path.parent / f"{input_path.stem}_PRESERVED_BIRD_{timestamp}.jpg"
            
            # Save with maximum quality (no modifications to bird pixels)
            cv2.imwrite(str(output_path), cropped_image, [cv2.IMWRITE_JPEG_QUALITY, 100])
            
            logger.info(f"Preserved bird image saved: {output_path}")
            logger.info(f"Final size: {cropped_image.shape[1]}x{cropped_image.shape[0]}")
            
            # Calculate how much padding was removed
            original_pixels = image.shape[0] * image.shape[1]
            final_pixels = cropped_image.shape[0] * cropped_image.shape[1]
            padding_removed = original_pixels - final_pixels
            
            logger.info(f"Black padding pixels removed: {padding_removed}")
            logger.info(f"Bird pixels preserved: 100%")
            
            return str(output_path)
            
        except Exception as e:
            logger.error(f"Error in preserve bird cropping: {e}")
            raise
    
    def _detect_pure_black_padding(self, image: np.ndarray) -> np.ndarray:
        """
        Detect ONLY pure black padding pixels using edge-based analysis
        This ensures we don't accidentally remove any bird pixels
        """
        try:
            # Convert to grayscale for analysis
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

            # Method 1: Detect very dark pixels
            _, gray_black = cv2.threshold(gray, self.black_threshold, 255, cv2.THRESH_BINARY_INV)

            # Method 2: Check all color channels
            b, g, r = cv2.split(image)

            # All channels must be below threshold
            dark_b = b <= self.black_threshold
            dark_g = g <= self.black_threshold
            dark_r = r <= self.black_threshold

            # Only pixels that are dark in ALL channels
            all_channels_dark = np.logical_and(np.logical_and(dark_b, dark_g), dark_r)
            color_black = all_channels_dark.astype(np.uint8) * 255

            # Combine methods
            combined_black = cv2.bitwise_and(gray_black, color_black)

            # Method 3: Edge-based detection - find continuous black regions from edges
            edge_black = self._find_edge_connected_black_regions(combined_black)

            # Count detected black pixels
            black_pixel_count = np.sum(edge_black > 0)
            total_pixels = image.shape[0] * image.shape[1]
            black_percentage = (black_pixel_count / total_pixels) * 100

            logger.info(f"Detected {black_pixel_count} edge-connected black padding pixels ({black_percentage:.2f}% of image)")

            return edge_black

        except Exception as e:
            logger.error(f"Error detecting black padding: {e}")
            # Return empty mask if error - better to preserve everything
            return np.zeros(image.shape[:2], dtype=np.uint8)

    def _find_edge_connected_black_regions(self, black_mask: np.ndarray) -> np.ndarray:
        """
        Find black regions that are connected to the image edges (likely padding)
        This prevents removing isolated dark areas within the bird
        """
        try:
            height, width = black_mask.shape

            # Create a mask for edge-connected regions
            edge_connected = np.zeros_like(black_mask)

            # Start flood fill from all edge pixels that are black
            edges = [
                # Top edge
                [(0, j) for j in range(width) if black_mask[0, j] > 0],
                # Bottom edge
                [(height-1, j) for j in range(width) if black_mask[height-1, j] > 0],
                # Left edge
                [(i, 0) for i in range(height) if black_mask[i, 0] > 0],
                # Right edge
                [(i, width-1) for i in range(height) if black_mask[i, width-1] > 0]
            ]

            # Flatten the edge points
            edge_points = []
            for edge_list in edges:
                edge_points.extend(edge_list)

            # For each edge point, flood fill to find connected black regions
            for start_point in edge_points:
                if edge_connected[start_point] == 0:  # Not already processed
                    self._flood_fill_connected_black(black_mask, edge_connected, start_point)

            return edge_connected

        except Exception as e:
            logger.error(f"Error finding edge-connected regions: {e}")
            return black_mask

    def _flood_fill_connected_black(self, black_mask: np.ndarray, result_mask: np.ndarray, start_point: tuple):
        """
        Flood fill to find all black pixels connected to the starting point
        """
        try:
            height, width = black_mask.shape
            stack = [start_point]

            while stack:
                y, x = stack.pop()

                # Check bounds
                if y < 0 or y >= height or x < 0 or x >= width:
                    continue

                # Check if already processed or not black
                if result_mask[y, x] > 0 or black_mask[y, x] == 0:
                    continue

                # Mark as edge-connected black
                result_mask[y, x] = 255

                # Add neighbors to stack
                neighbors = [
                    (y-1, x), (y+1, x), (y, x-1), (y, x+1),  # 4-connected
                    (y-1, x-1), (y-1, x+1), (y+1, x-1), (y+1, x+1)  # 8-connected
                ]

                for ny, nx in neighbors:
                    stack.append((ny, nx))

        except Exception as e:
            logger.error(f"Error in flood fill: {e}")
    
    def _create_edge_mask(self, image_shape: tuple, edge_width: int = 20) -> np.ndarray:
        """
        Create a mask that only includes edge areas where padding is likely to be
        This prevents accidentally removing dark bird pixels in the center
        """
        try:
            height, width = image_shape
            mask = np.zeros((height, width), dtype=np.uint8)
            
            # Top edge
            mask[0:edge_width, :] = 255
            # Bottom edge  
            mask[height-edge_width:height, :] = 255
            # Left edge
            mask[:, 0:edge_width] = 255
            # Right edge
            mask[:, width-edge_width:width] = 255
            
            return mask
            
        except Exception as e:
            logger.error(f"Error creating edge mask: {e}")
            # Return full mask if error
            return np.ones(image_shape, dtype=np.uint8) * 255
    
    def _find_content_bounding_box(self, content_mask: np.ndarray) -> tuple:
        """
        Find the minimal bounding box around ALL content (non-black pixels)
        """
        try:
            # Find all non-zero points (content pixels)
            points = cv2.findNonZero(content_mask)
            
            if points is None:
                logger.warning("No content points found")
                return None
            
            # Get bounding rectangle around all content
            x, y, w, h = cv2.boundingRect(points)
            
            logger.info(f"Content bounding box: x={x}, y={y}, w={w}, h={h}")
            
            return (x, y, w, h)
            
        except Exception as e:
            logger.error(f"Error finding content bounding box: {e}")
            return None
    
    def _crop_to_content_area(self, image: np.ndarray, bbox: tuple) -> np.ndarray:
        """
        Crop image to the content area - preserves ALL pixels within the bounding box
        """
        try:
            x, y, w, h = bbox
            
            # Simple crop - no pixel modification, just boundary adjustment
            cropped = image[y:y+h, x:x+w]
            
            logger.info(f"Cropped from {image.shape[1]}x{image.shape[0]} to {cropped.shape[1]}x{cropped.shape[0]}")
            
            return cropped
            
        except Exception as e:
            logger.error(f"Error cropping to content area: {e}")
            return image
    
    def analyze_image_content(self, image_path: str) -> dict:
        """
        Analyze the image to show what will be detected as black padding
        (for debugging and verification)
        """
        try:
            image = cv2.imread(image_path)
            if image is None:
                return {"error": "Could not load image"}
            
            # Detect black padding
            black_mask = self._detect_pure_black_padding(image)
            content_mask = cv2.bitwise_not(black_mask)
            
            # Calculate statistics
            total_pixels = image.shape[0] * image.shape[1]
            black_pixels = np.sum(black_mask > 0)
            content_pixels = np.sum(content_mask > 0)
            
            # Find bounding boxes
            content_bbox = self._find_content_bounding_box(content_mask)
            
            analysis = {
                "original_size": f"{image.shape[1]}x{image.shape[0]}",
                "total_pixels": total_pixels,
                "black_padding_pixels": black_pixels,
                "content_pixels": content_pixels,
                "black_percentage": f"{(black_pixels/total_pixels)*100:.2f}%",
                "content_percentage": f"{(content_pixels/total_pixels)*100:.2f}%",
                "content_bbox": content_bbox,
                "final_size": f"{content_bbox[2]}x{content_bbox[3]}" if content_bbox else "Unknown"
            }
            
            return analysis
            
        except Exception as e:
            return {"error": str(e)}


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Preserve Bird Cropper - Remove Only Black Background Padding",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Remove only black padding, preserve all bird pixels
  python preserve_bird_cropper.py --input 00_original_PERFECT_CROP_1px.jpg
  
  # Analyze what will be detected as black padding
  python preserve_bird_cropper.py --input image.jpg --analyze-only
  
  # Use custom black threshold (more conservative)
  python preserve_bird_cropper.py --input image.jpg --black-threshold 20
        """
    )
    
    parser.add_argument('--input', '-i', type=str, required=True, help='Input image path')
    parser.add_argument('--output', '-o', type=str, help='Output image path (optional)')
    parser.add_argument('--black-threshold', type=int, default=25,
                       help='Threshold for detecting black pixels (default: 25, lower = more conservative)')
    parser.add_argument('--analyze-only', action='store_true',
                       help='Only analyze the image, do not process')
    
    args = parser.parse_args()
    
    # Create preserve bird cropper instance
    cropper = PreserveBirdCropper(black_threshold=args.black_threshold)
    
    if args.analyze_only:
        # Just analyze the image
        analysis = cropper.analyze_image_content(args.input)
        print("\n" + "="*50)
        print("IMAGE ANALYSIS")
        print("="*50)
        for key, value in analysis.items():
            print(f"{key.replace('_', ' ').title()}: {value}")
        print("="*50)
    else:
        # Process the image
        try:
            result = cropper.remove_black_padding_only(args.input, args.output)
            print(f"✅ Successfully preserved bird and removed padding: {result}")
        except Exception as e:
            print(f"❌ Failed to process image: {e}")


if __name__ == "__main__":
    main()
