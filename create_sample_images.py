#!/usr/bin/env python3
"""
Sample Image Generator for Bird Cropping Bot Testing
===================================================

This script creates synthetic bird images for testing the cropping bot
functionality. It generates various scenarios including different backgrounds,
bird positions, and image compositions.

Author: AI Assistant
Version: 1.0.0
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os
from pathlib import Path
import random
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SampleImageGenerator:
    """Generate sample bird images for testing"""
    
    def __init__(self, output_dir: str = "sample_images"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        logger.info(f"Sample images will be saved to: {self.output_dir}")
    
    def create_all_samples(self):
        """Create all types of sample images"""
        logger.info("Creating sample bird images for testing...")
        
        # Create different test scenarios
        self.create_centered_bird()
        self.create_off_center_bird()
        self.create_multiple_birds()
        self.create_bird_with_complex_background()
        self.create_bird_silhouette()
        self.create_bird_close_up()
        self.create_bird_in_flight()
        self.create_small_bird_large_background()
        
        logger.info(f"Sample images created in: {self.output_dir}")
        self._create_readme()
    
    def create_centered_bird(self):
        """Create image with bird in center"""
        img = np.ones((600, 800, 3), dtype=np.uint8) * 135  # Gray background
        
        # Draw simple bird shape in center
        center = (400, 300)
        
        # Bird body (ellipse)
        cv2.ellipse(img, center, (80, 120), 0, 0, 360, (101, 67, 33), -1)  # Brown
        
        # Bird head (circle)
        cv2.circle(img, (center[0], center[1] - 80), 50, (139, 69, 19), -1)  # Dark brown
        
        # Eye
        cv2.circle(img, (center[0] + 15, center[1] - 90), 8, (0, 0, 0), -1)  # Black
        cv2.circle(img, (center[0] + 18, center[1] - 93), 3, (255, 255, 255), -1)  # White highlight
        
        # Beak
        points = np.array([[center[0] + 35, center[1] - 75], 
                          [center[0] + 65, center[1] - 80], 
                          [center[0] + 40, center[1] - 85]], np.int32)
        cv2.fillPoly(img, [points], (255, 140, 0))  # Orange beak
        
        # Wing
        cv2.ellipse(img, (center[0] - 20, center[1] - 20), (40, 80), 15, 0, 360, (160, 82, 45), -1)
        
        cv2.imwrite(str(self.output_dir / "centered_bird.jpg"), img)
        logger.info("Created: centered_bird.jpg")
    
    def create_off_center_bird(self):
        """Create image with bird off-center"""
        img = np.ones((600, 800, 3), dtype=np.uint8) * 200  # Light gray background
        
        # Add some background texture
        for _ in range(100):
            x, y = random.randint(0, 799), random.randint(0, 599)
            cv2.circle(img, (x, y), random.randint(1, 3), (180, 180, 180), -1)
        
        # Bird in upper right
        center = (600, 150)
        
        # Bird body
        cv2.ellipse(img, center, (60, 90), 0, 0, 360, (70, 130, 180), -1)  # Blue-gray
        
        # Bird head
        cv2.circle(img, (center[0], center[1] - 60), 35, (50, 100, 150), -1)
        
        # Eye
        cv2.circle(img, (center[0] + 10, center[1] - 65), 6, (0, 0, 0), -1)
        cv2.circle(img, (center[0] + 12, center[1] - 67), 2, (255, 255, 255), -1)
        
        # Beak
        points = np.array([[center[0] + 25, center[1] - 60], 
                          [center[0] + 45, center[1] - 65], 
                          [center[0] + 30, center[1] - 70]], np.int32)
        cv2.fillPoly(img, [points], (100, 100, 100))
        
        cv2.imwrite(str(self.output_dir / "off_center_bird.jpg"), img)
        logger.info("Created: off_center_bird.jpg")
    
    def create_multiple_birds(self):
        """Create image with multiple birds"""
        img = np.ones((600, 800, 3), dtype=np.uint8) * 160  # Medium gray background
        
        # First bird (main subject)
        center1 = (300, 250)
        cv2.ellipse(img, center1, (70, 100), 0, 0, 360, (139, 69, 19), -1)
        cv2.circle(img, (center1[0], center1[1] - 70), 40, (160, 82, 45), -1)
        cv2.circle(img, (center1[0] + 12, center1[1] - 75), 6, (0, 0, 0), -1)
        
        # Second bird (smaller, background)
        center2 = (550, 400)
        cv2.ellipse(img, center2, (40, 60), 0, 0, 360, (100, 100, 100), -1)
        cv2.circle(img, (center2[0], center2[1] - 40), 25, (120, 120, 120), -1)
        cv2.circle(img, (center2[0] + 8, center2[1] - 45), 4, (0, 0, 0), -1)
        
        # Third bird (partial, edge)
        center3 = (750, 300)
        cv2.ellipse(img, center3, (50, 80), 0, 0, 180, (80, 80, 80), -1)
        
        cv2.imwrite(str(self.output_dir / "multiple_birds.jpg"), img)
        logger.info("Created: multiple_birds.jpg")
    
    def create_bird_with_complex_background(self):
        """Create bird with complex background"""
        # Create complex background
        img = np.random.randint(100, 200, (600, 800, 3), dtype=np.uint8)
        
        # Add some background elements (trees, branches)
        for _ in range(20):
            x1, y1 = random.randint(0, 800), random.randint(0, 600)
            x2, y2 = x1 + random.randint(-100, 100), y1 + random.randint(-100, 100)
            cv2.line(img, (x1, y1), (x2, y2), (101, 67, 33), random.randint(3, 8))
        
        # Add some leaves
        for _ in range(50):
            x, y = random.randint(0, 799), random.randint(0, 599)
            cv2.circle(img, (x, y), random.randint(5, 15), (34, 139, 34), -1)
        
        # Bird in foreground
        center = (400, 300)
        cv2.ellipse(img, center, (90, 130), 0, 0, 360, (220, 20, 60), -1)  # Red bird
        cv2.circle(img, (center[0], center[1] - 90), 55, (255, 69, 0), -1)  # Orange head
        cv2.circle(img, (center[0] + 15, center[1] - 100), 8, (0, 0, 0), -1)
        cv2.circle(img, (center[0] + 18, center[1] - 103), 3, (255, 255, 255), -1)
        
        # Prominent beak
        points = np.array([[center[0] + 40, center[1] - 85], 
                          [center[0] + 75, center[1] - 90], 
                          [center[0] + 45, center[1] - 95]], np.int32)
        cv2.fillPoly(img, [points], (255, 215, 0))
        
        cv2.imwrite(str(self.output_dir / "complex_background_bird.jpg"), img)
        logger.info("Created: complex_background_bird.jpg")
    
    def create_bird_silhouette(self):
        """Create bird silhouette against bright background"""
        img = np.ones((600, 800, 3), dtype=np.uint8) * 240  # Very light background

        # Add gradient background
        for y in range(600):
            intensity = int(240 - (y / 600) * 40)
            # Ensure values stay within uint8 bounds (0-255)
            r = min(255, max(0, intensity))
            g = min(255, max(0, intensity + 10))
            b = min(255, max(0, intensity + 15))  # Reduced to prevent overflow
            img[y, :] = [r, g, b]
        
        # Dark bird silhouette
        center = (350, 300)
        cv2.ellipse(img, center, (100, 140), 0, 0, 360, (20, 20, 20), -1)
        cv2.circle(img, (center[0], center[1] - 100), 60, (10, 10, 10), -1)
        
        # Wing details
        cv2.ellipse(img, (center[0] - 30, center[1] - 30), (50, 90), 20, 0, 360, (30, 30, 30), -1)
        
        cv2.imwrite(str(self.output_dir / "bird_silhouette.jpg"), img)
        logger.info("Created: bird_silhouette.jpg")
    
    def create_bird_close_up(self):
        """Create close-up bird image"""
        img = np.ones((600, 800, 3), dtype=np.uint8) * 180
        
        # Large bird head filling most of frame
        center = (400, 300)
        cv2.circle(img, center, 200, (139, 69, 19), -1)  # Large head
        
        # Eye details
        cv2.circle(img, (center[0] + 50, center[1] - 50), 30, (0, 0, 0), -1)
        cv2.circle(img, (center[0] + 55, center[1] - 55), 12, (255, 255, 255), -1)
        cv2.circle(img, (center[0] + 58, center[1] - 52), 8, (0, 0, 0), -1)
        
        # Large beak
        points = np.array([[center[0] + 120, center[1]], 
                          [center[0] + 220, center[1] - 20], 
                          [center[0] + 200, center[1] + 30],
                          [center[0] + 130, center[1] + 20]], np.int32)
        cv2.fillPoly(img, [points], (255, 140, 0))
        
        # Feather texture
        for _ in range(100):
            x = center[0] + random.randint(-180, 100)
            y = center[1] + random.randint(-180, 180)
            if (x - center[0])**2 + (y - center[1])**2 < 180**2:
                cv2.line(img, (x, y), (x + random.randint(-10, 10), y + random.randint(-10, 10)), 
                        (160, 82, 45), 1)
        
        cv2.imwrite(str(self.output_dir / "bird_close_up.jpg"), img)
        logger.info("Created: bird_close_up.jpg")
    
    def create_bird_in_flight(self):
        """Create bird in flight"""
        img = np.ones((600, 800, 3), dtype=np.uint8)
        
        # Sky gradient
        for y in range(600):
            intensity = int(200 + (y / 600) * 55)
            img[y, :] = [intensity, intensity, 255]
        
        # Flying bird
        center = (400, 200)
        
        # Body (more horizontal for flight)
        cv2.ellipse(img, center, (120, 40), 0, 0, 360, (101, 67, 33), -1)
        
        # Head
        cv2.circle(img, (center[0] + 80, center[1]), 30, (139, 69, 19), -1)
        
        # Wings spread
        # Left wing
        wing_points1 = np.array([[center[0] - 60, center[1] - 20], 
                               [center[0] - 150, center[1] - 80], 
                               [center[0] - 120, center[1] + 20]], np.int32)
        cv2.fillPoly(img, [wing_points1], (160, 82, 45))
        
        # Right wing
        wing_points2 = np.array([[center[0] - 60, center[1] + 20], 
                               [center[0] - 150, center[1] + 80], 
                               [center[0] - 120, center[1] - 20]], np.int32)
        cv2.fillPoly(img, [wing_points2], (160, 82, 45))
        
        # Eye
        cv2.circle(img, (center[0] + 85, center[1] - 5), 4, (0, 0, 0), -1)
        
        cv2.imwrite(str(self.output_dir / "bird_in_flight.jpg"), img)
        logger.info("Created: bird_in_flight.jpg")
    
    def create_small_bird_large_background(self):
        """Create small bird in large background (challenging for detection)"""
        img = np.ones((800, 1200, 3), dtype=np.uint8) * 190
        
        # Add background noise
        noise = np.random.randint(-30, 30, (800, 1200, 3), dtype=np.int16)
        img = np.clip(img.astype(np.int16) + noise, 0, 255).astype(np.uint8)
        
        # Very small bird in corner
        center = (200, 150)
        cv2.ellipse(img, center, (25, 35), 0, 0, 360, (70, 130, 180), -1)
        cv2.circle(img, (center[0], center[1] - 25), 15, (50, 100, 150), -1)
        cv2.circle(img, (center[0] + 5, center[1] - 28), 3, (0, 0, 0), -1)
        
        cv2.imwrite(str(self.output_dir / "small_bird_large_background.jpg"), img)
        logger.info("Created: small_bird_large_background.jpg")
    
    def _create_readme(self):
        """Create README for sample images"""
        readme_content = """# Sample Bird Images for Testing

This directory contains synthetic bird images created for testing the cropping bot functionality.

## Test Images:

1. **centered_bird.jpg** - Bird positioned in center of frame (easy case)
2. **off_center_bird.jpg** - Bird positioned off-center (moderate case)
3. **multiple_birds.jpg** - Multiple birds with one main subject (challenging)
4. **complex_background_bird.jpg** - Bird with complex background (challenging)
5. **bird_silhouette.jpg** - Bird silhouette against bright background (edge case)
6. **bird_close_up.jpg** - Close-up bird head (cropping precision test)
7. **bird_in_flight.jpg** - Bird with spread wings (shape variation)
8. **small_bird_large_background.jpg** - Small bird in large frame (detection challenge)

## Usage:

Test the cropping bot with these images:

```bash
# Test single image
python bird_cropping_bot.py --input sample_images/centered_bird.jpg

# Test all sample images
python bird_cropping_bot.py --input-dir sample_images --batch

# Test with different settings
python bird_cropping_bot.py --input sample_images/complex_background_bird.jpg --saliency fine_grained
```

## Expected Results:

- **Easy cases**: Should crop accurately with default settings
- **Moderate cases**: Should work well with appropriate saliency method
- **Challenging cases**: May require fine-tuned configuration
- **Edge cases**: Test robustness of fallback strategies

Add your own bird images to this directory for additional testing.
"""
        
        with open(self.output_dir / "README.md", 'w') as f:
            f.write(readme_content)
        
        logger.info("Created: README.md in sample_images directory")


def main():
    """Main entry point"""
    generator = SampleImageGenerator()
    generator.create_all_samples()
    print(f"\nSample images created successfully!")
    print(f"Location: {generator.output_dir}")
    print(f"Test the cropping bot with: python bird_cropping_bot.py --input-dir {generator.output_dir} --batch")


if __name__ == "__main__":
    main()
