#!/usr/bin/env python3
"""
Ultra Precise Bird Cropper - Perfect Edge Detection
===================================================

This script provides the most precise bird cropping possible, using advanced
contour analysis to crop exactly at the bird's outline with minimal background.

Author: AI Assistant
Version: 1.0.0
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
import os
import argparse
import logging
from pathlib import Path
from typing import Tuple, Optional, List
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UltraPreciseCropper:
    """Ultra-precise bird cropping with perfect edge detection"""
    
    def __init__(self, padding_pixels: int = 2):
        self.padding_pixels = padding_pixels
    
    def crop_ultra_precise(self, image_path: str, output_path: str = None) -> Optional[str]:
        """
        Crop image with ultra-precise edge detection around the bird
        
        Args:
            image_path: Path to input image
            output_path: Path for output image (optional)
            
        Returns:
            Path to cropped image or None if failed
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"Failed to load image: {image_path}")
                return None
            
            logger.info(f"Processing: {image_path}")
            logger.info(f"Original size: {image.shape[1]}x{image.shape[0]}")
            
            # Find the exact bird outline
            bird_mask = self._create_precise_bird_mask(image)
            if bird_mask is None:
                logger.warning("Could not create precise bird mask")
                return None
            
            # Get the tightest possible bounding box
            crop_region = self._get_minimal_bounding_box(bird_mask)
            if crop_region is None:
                logger.warning("Could not determine minimal bounding box")
                return None
            
            # Apply ultra-precise crop
            cropped_image = self._apply_ultra_precise_crop(image, crop_region)
            
            # Enhance the result
            enhanced_image = self._enhance_ultra_precise_crop(cropped_image)
            
            # Generate output path if not provided
            if output_path is None:
                input_path = Path(image_path)
                timestamp = int(time.time())
                output_path = input_path.parent / f"{input_path.stem}_ultra_precise_{timestamp}.jpg"
            
            # Save with maximum quality
            success = cv2.imwrite(str(output_path), enhanced_image, [cv2.IMWRITE_JPEG_QUALITY, 100])
            
            if success:
                logger.info(f"Ultra-precise cropped image saved: {output_path}")
                logger.info(f"Final size: {enhanced_image.shape[1]}x{enhanced_image.shape[0]}")
                return str(output_path)
            else:
                logger.error("Failed to save cropped image")
                return None
                
        except Exception as e:
            logger.error(f"Error in ultra-precise cropping: {e}")
            return None
    
    def _create_precise_bird_mask(self, image: np.ndarray) -> Optional[np.ndarray]:
        """Create the most precise bird mask possible"""
        try:
            # Convert to different color spaces for analysis
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            lab = cv2.cvtColor(image, cv2.COLOR_BGR2LAB)
            
            # Strategy 1: Multi-scale edge detection
            edges_mask = self._multi_scale_edge_detection(gray)
            
            # Strategy 2: Advanced color segmentation
            color_mask = self._advanced_color_segmentation(hsv)
            
            # Strategy 3: Texture-based segmentation
            texture_mask = self._texture_based_segmentation(gray)
            
            # Strategy 4: Gradient-based detection
            gradient_mask = self._gradient_based_detection(gray)
            
            # Combine all masks intelligently
            combined_mask = self._combine_masks_intelligently([
                edges_mask, color_mask, texture_mask, gradient_mask
            ])
            
            if combined_mask is None:
                return None
            
            # Refine the mask with morphological operations
            refined_mask = self._refine_mask(combined_mask)
            
            return refined_mask
            
        except Exception as e:
            logger.error(f"Error creating precise bird mask: {e}")
            return None
    
    def _multi_scale_edge_detection(self, gray: np.ndarray) -> Optional[np.ndarray]:
        """Multi-scale edge detection for better bird outline detection"""
        try:
            edges_combined = np.zeros_like(gray)
            
            # Multiple scales of Gaussian blur
            scales = [1, 3, 5]
            
            for scale in scales:
                # Apply Gaussian blur
                blurred = cv2.GaussianBlur(gray, (scale*2+1, scale*2+1), scale)
                
                # Canny edge detection with scale-adapted thresholds
                low_thresh = 30 + scale * 10
                high_thresh = 100 + scale * 20
                edges = cv2.Canny(blurred, low_thresh, high_thresh)
                
                # Add to combined edges
                edges_combined = cv2.bitwise_or(edges_combined, edges)
            
            # Morphological closing to connect nearby edges
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            edges_combined = cv2.morphologyEx(edges_combined, cv2.MORPH_CLOSE, kernel)
            
            return edges_combined
            
        except Exception as e:
            logger.error(f"Error in multi-scale edge detection: {e}")
            return None
    
    def _advanced_color_segmentation(self, hsv: np.ndarray) -> Optional[np.ndarray]:
        """Advanced color segmentation for bird detection"""
        try:
            # Define comprehensive color ranges for birds
            bird_color_ranges = [
                # Brown/tan birds
                ([8, 30, 30], [25, 255, 255]),
                # Gray birds
                ([0, 0, 30], [180, 30, 200]),
                # Dark birds (black, dark brown)
                ([0, 0, 0], [180, 255, 80]),
                # Red/orange birds
                ([0, 80, 80], [15, 255, 255]),
                # Blue birds
                ([90, 50, 50], [130, 255, 255]),
                # Green birds
                ([35, 40, 40], [85, 255, 255]),
                # Yellow birds
                ([20, 100, 100], [35, 255, 255]),
                # White/light birds
                ([0, 0, 180], [180, 30, 255])
            ]
            
            # Create masks for each color range
            masks = []
            for lower, upper in bird_color_ranges:
                lower = np.array(lower)
                upper = np.array(upper)
                mask = cv2.inRange(hsv, lower, upper)
                masks.append(mask)
            
            # Combine all color masks
            combined_color_mask = masks[0]
            for mask in masks[1:]:
                combined_color_mask = cv2.bitwise_or(combined_color_mask, mask)
            
            # Clean up the mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            combined_color_mask = cv2.morphologyEx(combined_color_mask, cv2.MORPH_CLOSE, kernel)
            combined_color_mask = cv2.morphologyEx(combined_color_mask, cv2.MORPH_OPEN, kernel)
            
            return combined_color_mask
            
        except Exception as e:
            logger.error(f"Error in advanced color segmentation: {e}")
            return None
    
    def _texture_based_segmentation(self, gray: np.ndarray) -> Optional[np.ndarray]:
        """Texture-based segmentation to detect bird features"""
        try:
            # Local Binary Pattern for texture analysis
            def local_binary_pattern(image, radius=3, n_points=24):
                """Simple LBP implementation"""
                h, w = image.shape
                lbp = np.zeros((h, w), dtype=np.uint8)
                
                for i in range(radius, h - radius):
                    for j in range(radius, w - radius):
                        center = image[i, j]
                        code = 0
                        for k in range(n_points):
                            angle = 2 * np.pi * k / n_points
                            x = int(i + radius * np.cos(angle))
                            y = int(j + radius * np.sin(angle))
                            if 0 <= x < h and 0 <= y < w:
                                if image[x, y] >= center:
                                    code |= (1 << k)
                        lbp[i, j] = code
                
                return lbp
            
            # Calculate LBP
            lbp = local_binary_pattern(gray)
            
            # Threshold LBP to find textured regions
            _, texture_mask = cv2.threshold(lbp, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            return texture_mask
            
        except Exception as e:
            logger.error(f"Error in texture-based segmentation: {e}")
            return None
    
    def _gradient_based_detection(self, gray: np.ndarray) -> Optional[np.ndarray]:
        """Gradient-based detection for bird edges"""
        try:
            # Calculate gradients in multiple directions
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            
            # Calculate gradient magnitude and direction
            magnitude = np.sqrt(grad_x**2 + grad_y**2)
            direction = np.arctan2(grad_y, grad_x)
            
            # Normalize magnitude
            magnitude = cv2.normalize(magnitude, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
            
            # Threshold to get strong gradients
            _, gradient_mask = cv2.threshold(magnitude, 60, 255, cv2.THRESH_BINARY)
            
            return gradient_mask
            
        except Exception as e:
            logger.error(f"Error in gradient-based detection: {e}")
            return None
    
    def _combine_masks_intelligently(self, masks: List[np.ndarray]) -> Optional[np.ndarray]:
        """Intelligently combine multiple masks"""
        try:
            valid_masks = [mask for mask in masks if mask is not None]
            if not valid_masks:
                return None
            
            # Start with the first valid mask
            combined = valid_masks[0].copy()
            
            # Combine masks using weighted voting
            for mask in valid_masks[1:]:
                # Use bitwise OR to combine
                combined = cv2.bitwise_or(combined, mask)
            
            # Apply morphological operations to clean up
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            combined = cv2.morphologyEx(combined, cv2.MORPH_CLOSE, kernel)
            combined = cv2.morphologyEx(combined, cv2.MORPH_OPEN, kernel)
            
            return combined
            
        except Exception as e:
            logger.error(f"Error combining masks: {e}")
            return None
    
    def _refine_mask(self, mask: np.ndarray) -> np.ndarray:
        """Refine the mask to get the best bird outline"""
        try:
            # Find contours
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return mask
            
            # Find the largest contour (assumed to be the bird)
            largest_contour = max(contours, key=cv2.contourArea)
            
            # Create a refined mask with only the largest contour
            refined_mask = np.zeros_like(mask)
            cv2.fillPoly(refined_mask, [largest_contour], 255)
            
            # Apply slight dilation to ensure we don't cut off bird edges
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            refined_mask = cv2.dilate(refined_mask, kernel, iterations=1)
            
            return refined_mask
            
        except Exception as e:
            logger.error(f"Error refining mask: {e}")
            return mask
    
    def _get_minimal_bounding_box(self, mask: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """Get the minimal bounding box around the bird"""
        try:
            # Find all non-zero points
            points = cv2.findNonZero(mask)
            if points is None:
                return None
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(points)
            
            # Add minimal padding
            x = max(0, x - self.padding_pixels)
            y = max(0, y - self.padding_pixels)
            w = min(mask.shape[1] - x, w + 2 * self.padding_pixels)
            h = min(mask.shape[0] - y, h + 2 * self.padding_pixels)
            
            logger.info(f"Minimal bounding box: x={x}, y={y}, w={w}, h={h}")
            
            return (x, y, w, h)
            
        except Exception as e:
            logger.error(f"Error getting minimal bounding box: {e}")
            return None
    
    def _apply_ultra_precise_crop(self, image: np.ndarray, crop_region: Tuple[int, int, int, int]) -> np.ndarray:
        """Apply ultra-precise crop"""
        x, y, w, h = crop_region
        cropped = image[y:y+h, x:x+w]
        return cropped
    
    def _enhance_ultra_precise_crop(self, image: np.ndarray) -> np.ndarray:
        """Enhance the ultra-precise cropped image"""
        try:
            # Convert to PIL for enhancement
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # Enhance sharpness more aggressively
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(1.4)
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.2)
            
            # Enhance color saturation slightly
            enhancer = ImageEnhance.Color(pil_image)
            pil_image = enhancer.enhance(1.1)
            
            # Convert back to OpenCV format
            enhanced = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return enhanced
            
        except Exception as e:
            logger.error(f"Error enhancing image: {e}")
            return image


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Ultra Precise Bird Cropper - Perfect Edge Detection",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Ultra-precise crop with minimal padding
  python ultra_precise_cropper.py --input 00_original.png
  
  # Ultra-precise crop with zero padding
  python ultra_precise_cropper.py --input 00_original.png --padding 0
  
  # Ultra-precise crop with custom output
  python ultra_precise_cropper.py --input 00_original.png --output perfect_bird_ultra.jpg
        """
    )
    
    parser.add_argument('--input', '-i', type=str, required=True, help='Input image path')
    parser.add_argument('--output', '-o', type=str, help='Output image path (optional)')
    parser.add_argument('--padding', '-p', type=int, default=2, help='Padding pixels around bird (default: 2)')
    
    args = parser.parse_args()
    
    # Create ultra-precise cropper instance
    cropper = UltraPreciseCropper(padding_pixels=args.padding)
    
    # Process the image
    result = cropper.crop_ultra_precise(args.input, args.output)
    
    if result:
        print(f"✅ Successfully created ultra-precise cropped image: {result}")
    else:
        print("❌ Failed to crop image with ultra-precision")


if __name__ == "__main__":
    main()
