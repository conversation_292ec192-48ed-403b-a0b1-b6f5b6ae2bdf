#!/usr/bin/env python3
"""
eBird Scraper + Cropping Bot Integration Pipeline
================================================

This script demonstrates how to integrate the bird cropping bot with
eBird scrapers for automated bird image collection and processing.

Author: AI Assistant
Version: 1.0.0
"""

import os
import sys
import time
import json
import argparse
from pathlib import Path
from typing import Dict, List
import logging

# Import cropping bot modules
from bird_cropping_bot import IntelligentCropper, CroppingConfig, load_config
from batch_crop import AdvancedBatchProcessor

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ebird_pipeline.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class eBirdCroppingPipeline:
    """Automated pipeline for eBird scraping and cropping"""
    
    def __init__(self, config_path: str = "cropping_config.json"):
        self.config = load_config(config_path)
        self.cropper = IntelligentCropper(self.config)
        self.batch_processor = AdvancedBatchProcessor(self.config, max_workers=4)
        
        # Pipeline directories
        self.screenshots_dir = Path("screenshots")
        self.cropped_dir = Path("cropped_birds")
        self.reports_dir = Path("reports")
        
        # Create directories
        for directory in [self.screenshots_dir, self.cropped_dir, self.reports_dir]:
            directory.mkdir(exist_ok=True)
    
    def run_full_pipeline(self, species: str = None, limit: int = 50, 
                         scraper_script: str = None) -> Dict:
        """
        Run the complete pipeline: scraping + cropping
        
        Args:
            species: Bird species to search for
            limit: Maximum number of images to process
            scraper_script: Path to eBird scraper script
            
        Returns:
            Pipeline results dictionary
        """
        logger.info("Starting eBird cropping pipeline...")
        start_time = time.time()
        
        pipeline_results = {
            'scraping_results': None,
            'cropping_results': None,
            'total_time': 0,
            'final_image_count': 0
        }
        
        try:
            # Step 1: Run eBird scraper (if provided)
            if scraper_script and os.path.exists(scraper_script):
                logger.info("Step 1: Running eBird scraper...")
                scraping_results = self._run_ebird_scraper(scraper_script, species, limit)
                pipeline_results['scraping_results'] = scraping_results
            else:
                logger.info("Step 1: Skipping scraper (using existing screenshots)")
                scraping_results = self._count_existing_screenshots()
                pipeline_results['scraping_results'] = scraping_results
            
            # Step 2: Process screenshots with cropping bot
            logger.info("Step 2: Processing screenshots with cropping bot...")
            cropping_results = self._process_screenshots()
            pipeline_results['cropping_results'] = cropping_results
            
            # Step 3: Generate comprehensive report
            logger.info("Step 3: Generating pipeline report...")
            report_path = self._generate_pipeline_report(pipeline_results)
            
            # Calculate final statistics
            pipeline_results['total_time'] = time.time() - start_time
            pipeline_results['final_image_count'] = cropping_results.successful_crops
            pipeline_results['report_path'] = report_path
            
            logger.info(f"Pipeline completed successfully in {pipeline_results['total_time']:.2f} seconds")
            logger.info(f"Final cropped images: {pipeline_results['final_image_count']}")
            
            return pipeline_results
            
        except Exception as e:
            logger.error(f"Pipeline failed: {e}")
            pipeline_results['error'] = str(e)
            return pipeline_results
    
    def _run_ebird_scraper(self, scraper_script: str, species: str, limit: int) -> Dict:
        """Run the eBird scraper script"""
        try:
            import subprocess
            
            # Build scraper command
            cmd = [sys.executable, scraper_script]
            if species:
                cmd.extend(['--species', species])
            if limit:
                cmd.extend(['--limit', str(limit)])
            
            # Add output directory
            cmd.extend(['--output-dir', str(self.screenshots_dir)])
            
            logger.info(f"Running scraper command: {' '.join(cmd)}")
            
            # Run scraper
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1 hour timeout
            
            if result.returncode == 0:
                logger.info("eBird scraper completed successfully")
                return {
                    'success': True,
                    'stdout': result.stdout,
                    'stderr': result.stderr,
                    'images_scraped': self._count_screenshots()
                }
            else:
                logger.error(f"eBird scraper failed with return code {result.returncode}")
                logger.error(f"Error output: {result.stderr}")
                return {
                    'success': False,
                    'error': result.stderr,
                    'images_scraped': 0
                }
                
        except subprocess.TimeoutExpired:
            logger.error("eBird scraper timed out")
            return {'success': False, 'error': 'Timeout', 'images_scraped': 0}
        except Exception as e:
            logger.error(f"Error running eBird scraper: {e}")
            return {'success': False, 'error': str(e), 'images_scraped': 0}
    
    def _count_existing_screenshots(self) -> Dict:
        """Count existing screenshots in the directory"""
        try:
            count = self._count_screenshots()
            return {
                'success': True,
                'images_scraped': count,
                'message': f'Found {count} existing screenshots'
            }
        except Exception as e:
            return {'success': False, 'error': str(e), 'images_scraped': 0}
    
    def _count_screenshots(self) -> int:
        """Count screenshot files in the screenshots directory"""
        if not self.screenshots_dir.exists():
            return 0
        
        image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
        count = 0
        
        for file_path in self.screenshots_dir.iterdir():
            if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                count += 1
        
        return count
    
    def _process_screenshots(self):
        """Process screenshots with the cropping bot"""
        try:
            if not self.screenshots_dir.exists() or not any(self.screenshots_dir.iterdir()):
                logger.warning("No screenshots found to process")
                return None
            
            # Use batch processor for efficient processing
            result = self.batch_processor.process_directory(
                str(self.screenshots_dir), 
                recursive=True
            )
            
            # Move cropped images to dedicated directory
            if result.successful_files:
                self._organize_cropped_images(result.successful_files)
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing screenshots: {e}")
            return None
    
    def _organize_cropped_images(self, cropped_files: List[str]):
        """Organize cropped images into dedicated directory"""
        try:
            for cropped_file in cropped_files:
                source_path = Path(cropped_file)
                if source_path.exists():
                    # Create destination path
                    dest_path = self.cropped_dir / source_path.name
                    
                    # Move file
                    source_path.rename(dest_path)
                    logger.debug(f"Moved {source_path} to {dest_path}")
            
            logger.info(f"Organized {len(cropped_files)} cropped images in {self.cropped_dir}")
            
        except Exception as e:
            logger.error(f"Error organizing cropped images: {e}")
    
    def _generate_pipeline_report(self, pipeline_results: Dict) -> str:
        """Generate comprehensive pipeline report"""
        try:
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            report_path = self.reports_dir / f"pipeline_report_{timestamp}.json"
            
            # Prepare report data
            report_data = {
                'pipeline_summary': {
                    'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'total_processing_time': pipeline_results.get('total_time', 0),
                    'final_image_count': pipeline_results.get('final_image_count', 0)
                },
                'scraping_phase': pipeline_results.get('scraping_results', {}),
                'cropping_phase': {},
                'configuration': self.config.__dict__ if hasattr(self.config, '__dict__') else {},
                'directories': {
                    'screenshots': str(self.screenshots_dir),
                    'cropped_output': str(self.cropped_dir),
                    'reports': str(self.reports_dir)
                }
            }
            
            # Add cropping results if available
            cropping_results = pipeline_results.get('cropping_results')
            if cropping_results:
                report_data['cropping_phase'] = {
                    'total_images': cropping_results.total_images,
                    'successful_crops': cropping_results.successful_crops,
                    'failed_crops': cropping_results.failed_crops,
                    'success_rate': f"{(cropping_results.successful_crops / cropping_results.total_images * 100):.1f}%" if cropping_results.total_images > 0 else "0%",
                    'processing_time': cropping_results.processing_time,
                    'successful_files': cropping_results.successful_files[:10],  # First 10 for brevity
                    'failed_files': cropping_results.failed_files[:10]
                }
            
            # Save report
            with open(report_path, 'w') as f:
                json.dump(report_data, f, indent=2, default=str)
            
            logger.info(f"Pipeline report saved to: {report_path}")
            return str(report_path)
            
        except Exception as e:
            logger.error(f"Error generating pipeline report: {e}")
            return None
    
    def cleanup_old_files(self, days_old: int = 7):
        """Clean up old files from previous runs"""
        try:
            import time
            cutoff_time = time.time() - (days_old * 24 * 60 * 60)
            
            directories_to_clean = [self.screenshots_dir, self.cropped_dir, self.reports_dir]
            
            for directory in directories_to_clean:
                if directory.exists():
                    for file_path in directory.iterdir():
                        if file_path.is_file() and file_path.stat().st_mtime < cutoff_time:
                            file_path.unlink()
                            logger.debug(f"Cleaned up old file: {file_path}")
            
            logger.info(f"Cleaned up files older than {days_old} days")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


def main():
    """Main entry point for the pipeline"""
    parser = argparse.ArgumentParser(
        description="eBird Scraper + Cropping Bot Integration Pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run full pipeline with scraper
  python ebird_cropping_pipeline.py --scraper ebird_scraper.py --species "Northern Cardinal" --limit 50
  
  # Process existing screenshots only
  python ebird_cropping_pipeline.py --process-only
  
  # Run with custom configuration
  python ebird_cropping_pipeline.py --config custom_config.json --process-only
  
  # Cleanup old files
  python ebird_cropping_pipeline.py --cleanup --days 7
        """
    )
    
    parser.add_argument('--scraper', '-s', type=str, help='Path to eBird scraper script')
    parser.add_argument('--species', type=str, help='Bird species to search for')
    parser.add_argument('--limit', '-l', type=int, default=50, help='Maximum images to process')
    parser.add_argument('--config', '-c', type=str, default='cropping_config.json', 
                       help='Cropping configuration file')
    parser.add_argument('--process-only', action='store_true', 
                       help='Process existing screenshots only (skip scraping)')
    parser.add_argument('--cleanup', action='store_true', help='Clean up old files')
    parser.add_argument('--days', type=int, default=7, help='Days old for cleanup (default: 7)')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    
    args = parser.parse_args()
    
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Initialize pipeline
    pipeline = eBirdCroppingPipeline(args.config)
    
    # Handle cleanup
    if args.cleanup:
        pipeline.cleanup_old_files(args.days)
        return
    
    # Run pipeline
    if args.process_only:
        # Process existing screenshots only
        results = pipeline.run_full_pipeline(scraper_script=None)
    else:
        # Run full pipeline with scraper
        results = pipeline.run_full_pipeline(
            species=args.species,
            limit=args.limit,
            scraper_script=args.scraper
        )
    
    # Print summary
    print("\n" + "="*60)
    print("EBIRD CROPPING PIPELINE SUMMARY")
    print("="*60)
    
    if 'error' in results:
        print(f"❌ Pipeline failed: {results['error']}")
    else:
        scraping = results.get('scraping_results', {})
        cropping = results.get('cropping_results')
        
        print(f"📸 Images scraped: {scraping.get('images_scraped', 'N/A')}")
        
        if cropping:
            print(f"✂️  Images processed: {cropping.total_images}")
            print(f"✅ Successfully cropped: {cropping.successful_crops}")
            print(f"❌ Failed crops: {cropping.failed_crops}")
            
            if cropping.total_images > 0:
                success_rate = (cropping.successful_crops / cropping.total_images) * 100
                print(f"📊 Success rate: {success_rate:.1f}%")
        
        print(f"⏱️  Total time: {results.get('total_time', 0):.2f} seconds")
        print(f"🎯 Final image count: {results.get('final_image_count', 0)}")
        
        if 'report_path' in results:
            print(f"📋 Report saved to: {results['report_path']}")
    
    print("="*60)


if __name__ == "__main__":
    main()
