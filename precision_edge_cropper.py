#!/usr/bin/env python3
"""
Precision Edge Cropper for Perfect Bird Image Cropping
======================================================

This script provides ultra-precise edge detection and cropping specifically
designed to crop birds with perfect edge alignment, minimal background,
and maximum detail preservation.

Author: AI Assistant
Version: 1.0.0
"""

import cv2
import numpy as np
from PIL import Image, ImageEnhance
import os
import argparse
import logging
from pathlib import Path
from typing import Tuple, Optional, List
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PrecisionEdgeCropper:
    """Ultra-precise edge detection and cropping for bird images"""
    
    def __init__(self, padding_pixels: int = 5, min_contour_area: int = 1000):
        self.padding_pixels = padding_pixels
        self.min_contour_area = min_contour_area
    
    def crop_with_perfect_edges(self, image_path: str, output_path: str = None) -> Optional[str]:
        """
        Crop image with perfect edge detection around the bird
        
        Args:
            image_path: Path to input image
            output_path: Path for output image (optional)
            
        Returns:
            Path to cropped image or None if failed
        """
        try:
            # Load image
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"Failed to load image: {image_path}")
                return None
            
            logger.info(f"Processing: {image_path}")
            logger.info(f"Original size: {image.shape[1]}x{image.shape[0]}")
            
            # Apply multiple edge detection strategies
            crop_region = self._find_precise_bird_edges(image)
            
            if crop_region is None:
                logger.warning("Could not detect bird edges precisely")
                return None
            
            # Apply the crop
            cropped_image = self._apply_precise_crop(image, crop_region)
            
            # Enhance the cropped image
            enhanced_image = self._enhance_cropped_image(cropped_image)
            
            # Generate output path if not provided
            if output_path is None:
                input_path = Path(image_path)
                timestamp = int(time.time())
                output_path = input_path.parent / f"{input_path.stem}_precision_cropped_{timestamp}.jpg"
            
            # Save the result
            success = cv2.imwrite(str(output_path), enhanced_image, [cv2.IMWRITE_JPEG_QUALITY, 98])
            
            if success:
                logger.info(f"Precision cropped image saved: {output_path}")
                logger.info(f"Final size: {enhanced_image.shape[1]}x{enhanced_image.shape[0]}")
                return str(output_path)
            else:
                logger.error("Failed to save cropped image")
                return None
                
        except Exception as e:
            logger.error(f"Error in precision cropping: {e}")
            return None
    
    def _find_precise_bird_edges(self, image: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """Find precise edges of the bird using advanced techniques"""
        try:
            # Strategy 1: Advanced edge detection with morphological operations
            edge_region = self._advanced_edge_detection(image)
            if edge_region:
                logger.info("Using advanced edge detection")
                return edge_region
            
            # Strategy 2: Color-based segmentation with refinement
            color_region = self._refined_color_segmentation(image)
            if color_region:
                logger.info("Using refined color segmentation")
                return color_region
            
            # Strategy 3: Gradient-based edge detection
            gradient_region = self._gradient_edge_detection(image)
            if gradient_region:
                logger.info("Using gradient edge detection")
                return gradient_region
            
            # Strategy 4: Contour-based detection
            contour_region = self._contour_based_detection(image)
            if contour_region:
                logger.info("Using contour-based detection")
                return contour_region
            
            return None
            
        except Exception as e:
            logger.error(f"Error in edge detection: {e}")
            return None
    
    def _advanced_edge_detection(self, image: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """Advanced edge detection with multiple algorithms"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (5, 5), 0)
            
            # Multiple edge detection methods
            # 1. Canny edge detection with optimal thresholds
            edges_canny = cv2.Canny(blurred, 30, 100)
            
            # 2. Sobel edge detection
            sobel_x = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=3)
            sobel_y = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)
            edges_sobel = np.sqrt(sobel_x**2 + sobel_y**2).astype(np.uint8)
            edges_sobel = cv2.threshold(edges_sobel, 50, 255, cv2.THRESH_BINARY)[1]
            
            # 3. Laplacian edge detection
            edges_laplacian = cv2.Laplacian(blurred, cv2.CV_64F)
            edges_laplacian = np.absolute(edges_laplacian).astype(np.uint8)
            edges_laplacian = cv2.threshold(edges_laplacian, 30, 255, cv2.THRESH_BINARY)[1]
            
            # Combine edge maps
            combined_edges = cv2.bitwise_or(edges_canny, edges_sobel)
            combined_edges = cv2.bitwise_or(combined_edges, edges_laplacian)
            
            # Morphological operations to connect edges
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            combined_edges = cv2.morphologyEx(combined_edges, cv2.MORPH_CLOSE, kernel)
            combined_edges = cv2.morphologyEx(combined_edges, cv2.MORPH_DILATE, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(combined_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Find the largest meaningful contour
            valid_contours = [c for c in contours if cv2.contourArea(c) > self.min_contour_area]
            if not valid_contours:
                return None
            
            # Get the contour with the largest area
            largest_contour = max(valid_contours, key=cv2.contourArea)
            
            # Get tight bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # Add minimal padding
            x = max(0, x - self.padding_pixels)
            y = max(0, y - self.padding_pixels)
            w = min(image.shape[1] - x, w + 2 * self.padding_pixels)
            h = min(image.shape[0] - y, h + 2 * self.padding_pixels)
            
            return (x, y, w, h)
            
        except Exception as e:
            logger.error(f"Error in advanced edge detection: {e}")
            return None
    
    def _refined_color_segmentation(self, image: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """Refined color segmentation for bird detection"""
        try:
            # Convert to HSV for better color analysis
            hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
            
            # Create multiple masks for different bird colors
            masks = []
            
            # Brown/earth tones (common in birds)
            brown_lower = np.array([8, 50, 50])
            brown_upper = np.array([25, 255, 255])
            masks.append(cv2.inRange(hsv, brown_lower, brown_upper))
            
            # Gray tones
            gray_lower = np.array([0, 0, 40])
            gray_upper = np.array([180, 60, 200])
            masks.append(cv2.inRange(hsv, gray_lower, gray_upper))
            
            # Dark colors (black birds)
            dark_lower = np.array([0, 0, 0])
            dark_upper = np.array([180, 255, 80])
            masks.append(cv2.inRange(hsv, dark_lower, dark_upper))
            
            # Red/orange tones
            red_lower = np.array([0, 100, 100])
            red_upper = np.array([10, 255, 255])
            masks.append(cv2.inRange(hsv, red_lower, red_upper))
            
            # Blue tones
            blue_lower = np.array([100, 50, 50])
            blue_upper = np.array([130, 255, 255])
            masks.append(cv2.inRange(hsv, blue_lower, blue_upper))
            
            # Combine all masks
            combined_mask = masks[0]
            for mask in masks[1:]:
                combined_mask = cv2.bitwise_or(combined_mask, mask)
            
            # Morphological operations to clean up the mask
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Filter by area and find the best contour
            valid_contours = [c for c in contours if cv2.contourArea(c) > self.min_contour_area]
            if not valid_contours:
                return None
            
            # Get the largest contour
            largest_contour = max(valid_contours, key=cv2.contourArea)
            
            # Get tight bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # Add minimal padding
            x = max(0, x - self.padding_pixels)
            y = max(0, y - self.padding_pixels)
            w = min(image.shape[1] - x, w + 2 * self.padding_pixels)
            h = min(image.shape[0] - y, h + 2 * self.padding_pixels)
            
            return (x, y, w, h)
            
        except Exception as e:
            logger.error(f"Error in color segmentation: {e}")
            return None
    
    def _gradient_edge_detection(self, image: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """Gradient-based edge detection"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Calculate gradients
            grad_x = cv2.Sobel(gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(gray, cv2.CV_64F, 0, 1, ksize=3)
            
            # Calculate gradient magnitude
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)
            
            # Normalize and threshold
            gradient_magnitude = cv2.normalize(gradient_magnitude, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)
            _, binary_gradient = cv2.threshold(gradient_magnitude, 50, 255, cv2.THRESH_BINARY)
            
            # Morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            binary_gradient = cv2.morphologyEx(binary_gradient, cv2.MORPH_CLOSE, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(binary_gradient, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Find the largest meaningful contour
            valid_contours = [c for c in contours if cv2.contourArea(c) > self.min_contour_area]
            if not valid_contours:
                return None
            
            largest_contour = max(valid_contours, key=cv2.contourArea)
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # Add minimal padding
            x = max(0, x - self.padding_pixels)
            y = max(0, y - self.padding_pixels)
            w = min(image.shape[1] - x, w + 2 * self.padding_pixels)
            h = min(image.shape[0] - y, h + 2 * self.padding_pixels)
            
            return (x, y, w, h)
            
        except Exception as e:
            logger.error(f"Error in gradient edge detection: {e}")
            return None
    
    def _contour_based_detection(self, image: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """Contour-based detection with adaptive thresholding"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # Apply adaptive thresholding
            adaptive_thresh = cv2.adaptiveThreshold(
                gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )
            
            # Invert if needed (bird should be dark on light background)
            if np.mean(adaptive_thresh) > 127:
                adaptive_thresh = cv2.bitwise_not(adaptive_thresh)
            
            # Morphological operations
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
            adaptive_thresh = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_CLOSE, kernel)
            adaptive_thresh = cv2.morphologyEx(adaptive_thresh, cv2.MORPH_OPEN, kernel)
            
            # Find contours
            contours, _ = cv2.findContours(adaptive_thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return None
            
            # Find the largest meaningful contour
            valid_contours = [c for c in contours if cv2.contourArea(c) > self.min_contour_area]
            if not valid_contours:
                return None
            
            largest_contour = max(valid_contours, key=cv2.contourArea)
            
            # Get bounding rectangle
            x, y, w, h = cv2.boundingRect(largest_contour)
            
            # Add minimal padding
            x = max(0, x - self.padding_pixels)
            y = max(0, y - self.padding_pixels)
            w = min(image.shape[1] - x, w + 2 * self.padding_pixels)
            h = min(image.shape[0] - y, h + 2 * self.padding_pixels)
            
            return (x, y, w, h)
            
        except Exception as e:
            logger.error(f"Error in contour detection: {e}")
            return None
    
    def _apply_precise_crop(self, image: np.ndarray, crop_region: Tuple[int, int, int, int]) -> np.ndarray:
        """Apply precise crop to the image"""
        x, y, w, h = crop_region
        cropped = image[y:y+h, x:x+w]
        logger.info(f"Cropped region: x={x}, y={y}, w={w}, h={h}")
        return cropped
    
    def _enhance_cropped_image(self, image: np.ndarray) -> np.ndarray:
        """Enhance the cropped image for better quality"""
        try:
            # Convert to PIL for enhancement
            pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            
            # Enhance sharpness
            enhancer = ImageEnhance.Sharpness(pil_image)
            pil_image = enhancer.enhance(1.3)
            
            # Enhance contrast
            enhancer = ImageEnhance.Contrast(pil_image)
            pil_image = enhancer.enhance(1.15)
            
            # Convert back to OpenCV format
            enhanced = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
            return enhanced
            
        except Exception as e:
            logger.error(f"Error enhancing image: {e}")
            return image


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description="Precision Edge Cropper for Perfect Bird Image Cropping",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Crop with perfect edges (minimal padding)
  python precision_edge_cropper.py --input 00_original.png
  
  # Crop with custom padding
  python precision_edge_cropper.py --input 00_original.png --padding 10
  
  # Crop with custom output path
  python precision_edge_cropper.py --input 00_original.png --output perfect_bird.jpg
        """
    )
    
    parser.add_argument('--input', '-i', type=str, required=True, help='Input image path')
    parser.add_argument('--output', '-o', type=str, help='Output image path (optional)')
    parser.add_argument('--padding', '-p', type=int, default=5, help='Padding pixels around bird (default: 5)')
    parser.add_argument('--min-area', type=int, default=1000, help='Minimum contour area (default: 1000)')
    
    args = parser.parse_args()
    
    # Create cropper instance
    cropper = PrecisionEdgeCropper(
        padding_pixels=args.padding,
        min_contour_area=args.min_area
    )
    
    # Process the image
    result = cropper.crop_with_perfect_edges(args.input, args.output)
    
    if result:
        print(f"✅ Successfully created precision-cropped image: {result}")
    else:
        print("❌ Failed to crop image with precision")


if __name__ == "__main__":
    main()
